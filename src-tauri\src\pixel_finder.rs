extern crate directories;
extern crate winapi;

use image::{<PERSON><PERSON><PERSON>, RgbaImage};
use win_screenshot::prelude::*;
use winapi::shared::minwindef::LPARAM;
use winapi::shared::windef::HWND;
use winapi::um::winuser::{EnumWindows, GetForegroundWindow, GetWindowTextLengthW, GetWindowTextW};

use crate::types::{KeybindPixel, Pixel};

// Define a custom error type for window not found
#[derive(Debug)]
pub enum WindowError {
    NotFound,
}

// Function to normalize a string by removing whitespace and converting to lowercase
fn normalize_title(title: &str) -> String {
    title.trim().to_lowercase()
}

// Callback function for EnumWindows
unsafe extern "system" fn enum_windows_proc(hwnd: HWND, lparam: LPARAM) -> i32 {
    let target_title = &*(lparam as *const String);
    let length = GetWindowTextLengthW(hwnd) as usize;
    if length > 0 {
        let mut buffer: Vec<u16> = vec![0; length + 1];
        GetWindowTextW(hwnd, buffer.as_mut_ptr(), length as i32 + 1);
        let window_title = String::from_utf16_lossy(&buffer).trim().to_lowercase();

        if window_title.contains(target_title) {
            // Check if window is active
            if GetForegroundWindow() == hwnd {
                return 0; // Stop enumeration
            }
        }
    }
    1 // Continue enumeration
}

pub fn find_window_by_title(title: &str) -> Result<isize, WindowError> {
    let normalized_target_title = normalize_title(title);
    let target_title = Box::into_raw(Box::new(normalized_target_title)) as LPARAM;

    unsafe {
        if EnumWindows(Some(enum_windows_proc), target_title) == 1 {
            Err(WindowError::NotFound)
        } else {
            // Window found
            let hwnd = find_window(&*(target_title as *const String)).unwrap();
            Ok(hwnd)
        }
    }
}
pub fn read_pixels<'a>(hwnd: isize, keybinds: Vec<KeybindPixel>) -> Vec<Pixel> {
    let using: Using = Using::PrintWindow;
    let area = Area::ClientOnly;
    let crop_xy = Some([0, 0]);
    let crop_wh = Some([150, 1]);
    let buf = capture_window_ex(hwnd, using, area, crop_xy, crop_wh).unwrap();
    let img = RgbaImage::from_raw(buf.width, buf.height, buf.pixels).unwrap();

    if keybinds.is_empty() {
        println!("No keybinds found");
        return Vec::new();
    }

    let mut keybinds = keybinds.clone();

    let mut pixels: Vec<Pixel> = Vec::new();

    let default_keys: Vec<(i32, String)> = vec![
        (1, "Started".to_string()),
        (2, "Paused".to_string()),
        (3, "InCombat".to_string()),
    ];

    for default in default_keys {
        keybinds.push(KeybindPixel {
            pixel: default.0,
            name: default.1,
            key: None,
            key_code: 0,
            modifier: None,
        });
    }

    for x in 0..img.width() {
        let pixel = img.get_pixel(x, 0);

        if let Some(keybind) = keybinds.iter().find(|keybind| keybind.pixel == x as i32) {
            // Access the color values
            let Rgba(data) = pixel;
            let [r, g, b, _] = *data;
            pixels.push(Pixel {
                r,
                g,
                b,
                key: keybind.key.clone(),
                key_code: keybind.key_code,
                name: keybind.name.clone(),
                x: x,
                should_press: g == 255,
                modifier: keybind.modifier.clone(),
            });
            println!("Pixel {}: {} - {}", x, keybind.name, g == 255);
        } else {
            continue;
        }
    }

    pixels
}
