import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { ChakraProvider } from "@chakra-ui/react";
import { FormProvider, useForm } from "react-hook-form";
import { ConditionModal } from "../ConditionModal";
import { CustomRotation } from "../../types";

// Mock form wrapper component
const TestWrapper: React.FC<{
  children: React.ReactNode;
  initialConditions?: any[];
}> = ({ children, initialConditions = [] }) => {
  const methods = useForm<CustomRotation>({
    defaultValues: {
      id: "test-rotation",
      name: "Test Rotation",
      class: "Warrior",
      game: "WoW",
      spec: "Arms",
      author: "Test Author",
      version: "1.0.0",
      sections: [
        {
          id: "section1",
          name: "Test Section",
          order: 1,
          actions: [
            {
              id: "action1",
              action_type: "CAST",
              order: 1,
              conditions: initialConditions,
              sectionId: "section1",
            },
          ],
        },
      ],
      spells: [
        { id: "spell1", name: "Mortal Strike", spell_ids: [12294] },
        { id: "spell2", name: "Overpower", spell_ids: [7384] },
      ],
      buffs: [
        { id: "buff1", name: "Battle Shout", spell_ids: [6673], type_: "BUFF" },
        { id: "debuff1", name: "Rend", spell_ids: [772], type_: "DEBUFF" },
      ],
      is_custom: true,
      type: "CUSTOM",
    },
  });

  return (
    <ChakraProvider>
      <FormProvider {...methods}>{children}</FormProvider>
    </ChakraProvider>
  );
};

const defaultProps = {
  isOpen: true,
  onClose: jest.fn(),
  name: "sections.0.actions.0.conditions",
  selectedConditionId: null,
  groupId: "test-group-id",
};

describe("ConditionModal", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the modal when open", () => {
    render(
      <TestWrapper>
        <ConditionModal {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText("Add Condition")).toBeInTheDocument();
    expect(screen.getByLabelText("Entity Type")).toBeInTheDocument();
    expect(screen.getByLabelText("Property Type")).toBeInTheDocument();
    expect(screen.getByLabelText("Operator")).toBeInTheDocument();
    expect(screen.getByLabelText("Value Type")).toBeInTheDocument();
    expect(screen.getByLabelText("Value")).toBeInTheDocument();
  });

  it("does not render when closed", () => {
    render(
      <TestWrapper>
        <ConditionModal {...defaultProps} isOpen={false} />
      </TestWrapper>
    );

    expect(screen.queryByText("Add Condition")).not.toBeInTheDocument();
  });

  it("shows correct property options for PLAYER entity type", async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ConditionModal {...defaultProps} />
      </TestWrapper>
    );

    const propertySelect = screen.getByLabelText("Property Type");
    await user.click(propertySelect);

    // PLAYER should have HEALTH, RESOURCE, HAS_BUFF options
    expect(screen.getByText("Health")).toBeInTheDocument();
    expect(screen.getByText("Resource")).toBeInTheDocument();
    expect(screen.getByText("Has Buff")).toBeInTheDocument();
  });

  it("shows correct property options for TARGET entity type", async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ConditionModal {...defaultProps} />
      </TestWrapper>
    );

    const entitySelect = screen.getByLabelText("Entity Type");
    await user.selectOptions(entitySelect, "TARGET");

    const propertySelect = screen.getByLabelText("Property Type");
    await user.click(propertySelect);

    // TARGET should have HEALTH, HAS_DEBUFF, DEBUFF_REMAINING options
    expect(screen.getByText("Health")).toBeInTheDocument();
    expect(screen.getByText("Has Debuff")).toBeInTheDocument();
    expect(screen.getByText("Debuff Remaining")).toBeInTheDocument();
  });

  it("shows spell selector when SPELL entity type is selected", async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ConditionModal {...defaultProps} />
      </TestWrapper>
    );

    const entitySelect = screen.getByLabelText("Entity Type");
    await user.selectOptions(entitySelect, "SPELL");

    await waitFor(() => {
      expect(screen.getByLabelText("Spell")).toBeInTheDocument();
    });

    const spellSelect = screen.getByLabelText("Spell");
    expect(spellSelect).toBeInTheDocument();
    expect(screen.getByText("Mortal Strike")).toBeInTheDocument();
    expect(screen.getByText("Overpower")).toBeInTheDocument();
  });

  it("shows buff selector when HAS_BUFF property is selected", async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ConditionModal {...defaultProps} />
      </TestWrapper>
    );

    // PLAYER entity type should be selected by default
    const propertySelect = screen.getByLabelText("Property Type");
    await user.selectOptions(propertySelect, "HAS_BUFF");

    await waitFor(() => {
      expect(screen.getByLabelText("Buff")).toBeInTheDocument();
    });

    const buffSelect = screen.getByLabelText("Buff");
    expect(buffSelect).toBeInTheDocument();
    expect(screen.getByText("Battle Shout")).toBeInTheDocument();
  });

  it("calls onClose when Cancel button is clicked", async () => {
    const user = userEvent.setup();
    const onCloseMock = jest.fn();

    render(
      <TestWrapper>
        <ConditionModal {...defaultProps} onClose={onCloseMock} />
      </TestWrapper>
    );

    const cancelButton = screen.getByText("Cancel");
    await user.click(cancelButton);

    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });

  it("calls onClose when modal close button is clicked", async () => {
    const user = userEvent.setup();
    const onCloseMock = jest.fn();

    render(
      <TestWrapper>
        <ConditionModal {...defaultProps} onClose={onCloseMock} />
      </TestWrapper>
    );

    const closeButton = screen.getByLabelText("Close");
    await user.click(closeButton);

    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });

  it("populates fields when editing an existing condition", async () => {
    // Create a test condition to edit
    const testCondition = {
      id: "test-condition-1",
      entityType: "TARGET",
      propertyType: "HEALTH",
      operator: "LESS_THAN",
      value: 25,
      valueType: "PERCENTAGE",
      groupId: "test-group-id",
    };

    const initialConditions = [testCondition];

    render(
      <TestWrapper initialConditions={initialConditions}>
        <ConditionModal
          {...defaultProps}
          selectedConditionId="test-condition-1"
        />
      </TestWrapper>
    );

    // Check that the modal shows "Edit Condition" instead of "Add Condition"
    expect(screen.getByText("Edit Condition")).toBeInTheDocument();

    // Check that all fields are populated with the condition's values
    const entitySelect = screen.getByLabelText(
      "Entity Type"
    ) as HTMLSelectElement;
    expect(entitySelect.value).toBe("TARGET");

    const propertySelect = screen.getByLabelText(
      "Property Type"
    ) as HTMLSelectElement;
    expect(propertySelect.value).toBe("HEALTH");

    const operatorSelect = screen.getByLabelText(
      "Operator"
    ) as HTMLSelectElement;
    expect(operatorSelect.value).toBe("LESS_THAN");

    const valueTypeSelect = screen.getByLabelText(
      "Value Type"
    ) as HTMLSelectElement;
    expect(valueTypeSelect.value).toBe("PERCENTAGE");

    const valueInput = screen.getByLabelText("Value") as HTMLInputElement;
    expect(valueInput.value).toBe("25");
  });

  it("validates percentage values correctly when editing", async () => {
    const user = userEvent.setup();

    // Create a test condition with percentage value type
    const testCondition = {
      id: "test-condition-2",
      entityType: "PLAYER",
      propertyType: "HEALTH",
      operator: "LESS_THAN",
      value: 50,
      valueType: "PERCENTAGE",
      groupId: "test-group-id",
    };

    const initialConditions = [testCondition];

    render(
      <TestWrapper initialConditions={initialConditions}>
        <ConditionModal
          {...defaultProps}
          selectedConditionId="test-condition-2"
        />
      </TestWrapper>
    );

    const valueInput = screen.getByLabelText("Value") as HTMLInputElement;

    // Clear the input and enter an invalid percentage
    await user.clear(valueInput);
    await user.type(valueInput, "150");

    // Try to submit the form to trigger validation
    const submitButton = screen.getByText("Update");
    await user.click(submitButton);

    // Check that validation error appears
    await waitFor(() => {
      expect(
        screen.getByText("Percentage cannot exceed 100")
      ).toBeInTheDocument();
    });

    // Clear and enter a valid percentage
    await user.clear(valueInput);
    await user.type(valueInput, "75");

    // Trigger validation by blurring the field
    await user.tab();

    // Check that error disappears
    await waitFor(() => {
      expect(
        screen.queryByText("Percentage cannot exceed 100")
      ).not.toBeInTheDocument();
    });
  });

  it("validates required spell selection when editing SPELL conditions", async () => {
    const user = userEvent.setup();

    // Create a test condition that requires spell selection
    const testCondition = {
      id: "test-condition-3",
      entityType: "SPELL",
      propertyType: "COOLDOWN",
      operator: "EQUAL",
      value: 0,
      valueType: "SECONDS",
      checkSpellId: "Mortal Strike",
      groupId: "test-group-id",
    };

    const initialConditions = [testCondition];

    render(
      <TestWrapper initialConditions={initialConditions}>
        <ConditionModal
          {...defaultProps}
          selectedConditionId="test-condition-3"
        />
      </TestWrapper>
    );

    // Check that spell selector is populated
    const spellSelect = screen.getByLabelText("Spell") as HTMLSelectElement;
    expect(spellSelect.value).toBe("Mortal Strike");

    // Clear the selection
    await user.selectOptions(spellSelect, "");

    // Try to submit (this would trigger validation)
    const submitButton = screen.getByText("Update");
    await user.click(submitButton);

    // Check that validation error appears
    await waitFor(() => {
      expect(screen.getByText("Please select a spell")).toBeInTheDocument();
    });
  });
});
