import {
  Box,
  VStack,
  HStack,
  Text,
  Icon,
  Collapse,
  useDisclosure,
  useColorModeValue,
} from "@chakra-ui/react";
import { ChevronDownIcon, ChevronRightIcon } from "@chakra-ui/icons";
import { useFormContext } from "react-hook-form";
import { CustomRotation } from "../types";

export const RotationTreeView = () => {
  const { watch } = useFormContext<CustomRotation>();
  const sections = watch("sections") || [];
  const spells = watch("spells") || [];

  return (
    <Box>
      <Text fontSize="lg" fontWeight="bold" mb={4}>
        Rotation Overview
      </Text>

      <VStack align="stretch" spacing={2}>
        {sections
          .sort((a, b) => a.order - b.order)
          .map((section) => (
            <SectionTreeNode
              key={section.id}
              section={section}
              spells={spells}
            />
          ))}

        {sections.length === 0 && (
          <Text color="gray.500" fontSize="sm" fontStyle="italic">
            No sections created yet
          </Text>
        )}
      </VStack>
    </Box>
  );
};

interface SectionTreeNodeProps {
  section: any;
  spells: any[];
}

const SectionTreeNode = ({ section, spells }: SectionTreeNodeProps) => {
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: true });
  const bgColor = useColorModeValue("white", "gray.800");
  const hoverBg = useColorModeValue("gray.100", "gray.700");

  const getSpellName = (spellId: string) => {
    const spell = spells.find((s) => s.id === spellId);
    return spell?.name || "Unknown Spell";
  };

  const getActionDisplay = (action: any) => {
    switch (action.action_type) {
      case "CAST":
        return `Cast: ${getSpellName(action.spellId)}`;
      case "WAIT":
        return `Wait: ${action.waitSeconds}s`;
      case "START_SECTION":
        return `Go to Section`;
      default:
        return action.action_type;
    }
  };

  const getConditionSummary = (conditions: any[]) => {
    if (conditions.length === 0) return "";
    if (conditions.length === 1) {
      const condition = conditions[0];
      if ("conditions" in condition) {
        return `(${condition.conditions.length} conditions)`;
      } else {
        return `(${condition.entityType} ${condition.propertyType})`;
      }
    }
    return `(${conditions.length} condition groups)`;
  };

  return (
    <Box
      bg={bgColor}
      borderRadius="md"
      border="1px"
      borderColor="gray.200"
      _dark={{ borderColor: "gray.600" }}
    >
      <HStack
        p={3}
        cursor="pointer"
        onClick={onToggle}
        _hover={{ bg: hoverBg }}
        borderRadius="md"
      >
        <Icon as={isOpen ? ChevronDownIcon : ChevronRightIcon} />
        <Text fontWeight="medium" fontSize="sm">
          {section.name || `Section ${section.order}`}
        </Text>
        <Text fontSize="xs" color="gray.500">
          ({section.actions?.length || 0} actions)
        </Text>
      </HStack>

      <Collapse in={isOpen}>
        <VStack align="stretch" spacing={1} pl={6} pr={3} pb={3}>
          {section.actions
            ?.sort((a: any, b: any) => a.order - b.order)
            .map((action: any, index: number) => (
              <HStack
                key={action.id}
                p={2}
                bg={useColorModeValue("gray.50", "gray.700")}
                borderRadius="sm"
                fontSize="xs"
                spacing={2}
              >
                <Text color="gray.500" minW="4">
                  {index + 1}.
                </Text>
                <Text flex={1}>{getActionDisplay(action)}</Text>
                {action.conditions && action.conditions.length > 0 && (
                  <Text color="blue.500" fontSize="xs">
                    {getConditionSummary(action.conditions)}
                  </Text>
                )}
              </HStack>
            ))}

          {(!section.actions || section.actions.length === 0) && (
            <Text color="gray.400" fontSize="xs" pl={2}>
              No actions
            </Text>
          )}
        </VStack>
      </Collapse>
    </Box>
  );
};
