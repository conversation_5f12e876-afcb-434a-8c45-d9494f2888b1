{"id": "test-warrior-rotation", "name": "Test Warrior Rotation", "class": "WARRIOR", "game": "WoW", "spec": "Arms", "author": "TestUser", "version": "1.0.0", "is_custom": true, "spells": [{"name": "Mortal Strike", "spell_ids": [12294]}, {"name": "Overpower", "spell_ids": [7384]}, {"name": "Heroic Strike", "spell_ids": [78]}, {"name": "Rend", "spell_ids": [772]}], "buffs": [{"name": "Battle Shout", "spell_ids": [6673], "type_": "BUFF"}], "sections": [{"id": "opener", "name": "Opener", "order": 1, "actions": [{"id": "opener-rend", "action_type": "CAST", "order": 1, "spellId": "rend", "conditions": [{"id": "target-no-rend", "entityType": "TARGET", "propertyType": "HAS_DEBUFF", "operator": "EQUAL", "value": false, "valueType": "BOOLEAN"}]}, {"id": "opener-mortal-strike", "action_type": "CAST", "order": 2, "spellId": "mortal-strike", "conditions": [{"id": "enough-rage", "entityType": "PLAYER", "propertyType": "RESOURCE", "operator": "GREATER_THAN_OR_EQUAL", "value": 30, "valueType": "NUMBER"}]}]}, {"id": "rotation", "name": "Main Rotation", "order": 2, "actions": [{"id": "maintain-rend", "action_type": "CAST", "order": 1, "spellId": "rend", "conditions": [{"id": "rend-expiring", "entityType": "TARGET", "propertyType": "DEBUFF_REMAINING", "operator": "LESS_THAN", "value": 3, "valueType": "NUMBER"}]}, {"id": "mortal-strike-rotation", "action_type": "CAST", "order": 2, "spellId": "mortal-strike", "conditions": [{"id": "mortal-strike-ready", "entityType": "SPELL", "propertyType": "COOLDOWN", "operator": "EQUAL", "value": 0, "valueType": "NUMBER"}, {"id": "enough-rage-mortal", "entityType": "PLAYER", "propertyType": "RESOURCE", "operator": "GREATER_THAN_OR_EQUAL", "value": 30, "valueType": "NUMBER"}]}, {"id": "wait-for-rage", "action_type": "WAIT", "order": 3, "waitSeconds": 0.1, "conditions": []}]}]}