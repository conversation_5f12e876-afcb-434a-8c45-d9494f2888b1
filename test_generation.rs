// Test the Lua generation functionality
use std::fs;
use std::path::Path;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 Testing Lua generation from custom rotations...");

    // Check if we have the test JSON file
    let test_file = "custom_rotations/test_warrior_rotation.json";
    if !Path::new(test_file).exists() {
        println!("❌ Test file {} not found", test_file);
        return Ok(());
    }

    println!("✅ Test JSON file found");

    // Read and parse the JSON file
    let contents = fs::read_to_string(test_file)?;
    println!("✅ Successfully read JSON file ({} bytes)", contents.len());

    // Check if it looks like JSON
    if contents.trim().starts_with("{") && contents.trim().ends_with("}") {
        println!("✅ File appears to be valid JSON format");
    } else {
        println!("❌ File doesn't appear to be JSON format");
    }

    // Check if rotation_objects.lua exists
    if Path::new("src-tauri/rotation_objects.lua").exists() {
        println!("✅ rotation_objects.lua found");
    } else {
        println!("❌ rotation_objects.lua not found");
    }

    // Test the expected output structure
    let expected_folder = "custom_rotations/wow/warrior/test_warrior_rotation";
    println!("📁 Expected output folder: {}", expected_folder);

    // Check if the folder structure would be created correctly
    let game = "wow";
    let class = "warrior";
    let rotation_name = "test_warrior_rotation";

    println!("🎯 Target structure:");
    println!("   Game: {}", game);
    println!("   Class: {}", class);
    println!("   Rotation: {}", rotation_name);

    println!("✅ Test completed - ready for Lua generation!");

    Ok(())
}
