import {
  Flex,
  <PERSON><PERSON>,
  <PERSON>,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Input,
  Button,
  Box,
  useToast,
} from "@chakra-ui/react";
import "./Keybinds.css";
import { invoke } from "@tauri-apps/api";
import { useEffect, useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { FormKeybind, SavedKeybind, SavedRotationKeybinds } from "../types";
import { useSettingsStore } from "../stores/store";

const mapSavedKeybindsToFormValues = (
  savedKeybinds: SavedKeybind[]
): FormKeybind[] => {
  return savedKeybinds
    .map((keybind) => {
      let display_key = "";
      if (keybind.key) {
        if (keybind.modifier) {
          display_key = `${keybind.modifier}-${keybind.key}`.toUpperCase();
        } else {
          display_key = keybind.key.toUpperCase();
        }
      }
      return {
        ...keybind,
        display_key,
      };
    })
    .sort((a, b) => a.name.localeCompare(b.name));
};

const eventModifier = (e: React.KeyboardEvent<HTMLInputElement>) => {
  if (e.nativeEvent.ctrlKey) {
    return "Ctrl";
  } else if (e.nativeEvent.altKey) {
    return "Alt";
  } else if (e.nativeEvent.shiftKey) {
    return "Shift";
  } else {
    return "";
  }
};

export const Keybinds = () => {
  const [keybinds, setKeybinds] = useState<FormKeybind[]>([]);
  const [rotationId, setRotationId] = useState<string>("");
  const { selected_rotation } = useSettingsStore();
  const [activeModifier, setActiveModifier] = useState<
    string | null | undefined
  >(null);
  const toast = useToast();
  const { control, handleSubmit, setValue, reset, watch } = useForm({
    defaultValues: { keybinds },
  });

  useEffect(() => {
    if (!selected_rotation?.name) return;
    setKeybinds([]);
    reset({ keybinds: [] });
    invoke<SavedRotationKeybinds>("get_keybinds", {
      payload: JSON.stringify({
        rotation: selected_rotation.name,
        class: selected_rotation.class,
        game: selected_rotation.game,
      }),
    }).then((data) => {
      const mapDisplayKeys: FormKeybind[] = mapSavedKeybindsToFormValues(
        data.keybinds
      );
      setKeybinds(mapDisplayKeys);
      setRotationId(data.rotation_id);
      reset({ keybinds: mapDisplayKeys });
    });
  }, [selected_rotation.name]);

  const onSubmit = (data: any) => {
    // Convert the keybinds array to the format expected by the backend
    const mapped_keybinds: SavedKeybind[] = data.keybinds.map(
      (keybind: SavedKeybind, index: number) => ({
        ...keybinds[index],
        ...keybind,
        display_key: undefined,
      })
    );
    const input: SavedRotationKeybinds = {
      keybinds: mapped_keybinds,
      rotation_id: rotationId,
    };

    invoke<SavedKeybind[]>("set_keybinds", {
      payload: JSON.stringify(input),
    });
  };

  const getName = (name: string) => {
    if (name === "OptiT1") {
      return "Trinket 1";
    } else if (name === "OptiT2") {
      return "Trinket 2";
    }
    return name;
  };

  const handleKeyUp = (
    event: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    event.preventDefault();
    console.log(event);
    const code = event.code;
    const key = event.key;
    const keyCode = event.keyCode;
    let keyVal = code;
    if (code.startsWith("Digit")) {
      keyVal = code.replace("Digit", "");
    } else if (code.startsWith("Key")) {
      keyVal = code.replace("Key", "");
    }

    const eventMod = eventModifier(event);
    const current_key_is_modifier =
      event.key === "Control" || event.key === "Alt" || event.key === "Shift";

    if (current_key_is_modifier) {
      if (!activeModifier) {
        setActiveModifier(key);
        return;
      } else {
        setActiveModifier(null);
        return;
      }
    }

    // Extract raw value from code

    if (eventMod) {
      console.log(keyCode);
      if (keyCode <= 47 || keyCode >= 90) {
        console.log(keyCode);
        const keyname = eventMod ? `${eventMod}-${keyVal}` : keyVal;
        toast({
          title: "Could not bind key",
          description: `Binding ${keyname} is not allowed.`,
          status: "error",
          duration: 3000,
          isClosable: true,
        });
        return;
      }
      keyVal = String.fromCharCode(keyCode);
    }

    // Handle numpad keys
    if (keyVal === "NumpadAdd") keyVal = "NumpadPlus";
    if (keyVal === "NumpadSubtract") keyVal = "NumpadMinus";

    // Combine modifier with key if present
    const finalKey = eventMod ? `${eventMod}-${keyVal}` : keyVal;
    console.log("finalKey", finalKey);
    // Check for existing keybind
    const existingKeybind = watch("keybinds").find(
      (k, i) =>
        i !== index && k.display_key.toUpperCase() === finalKey.toUpperCase()
    );

    if (existingKeybind) {
      toast({
        title: "Key already in use",
        description: `This key is already bound to ${existingKeybind.name}`,
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const modifier = eventMod ? eventMod.toUpperCase() : "";

    setValue(`keybinds.${index}.key`, keyVal.toUpperCase());
    setValue(`keybinds.${index}.key_code`, keyCode);
    setValue(`keybinds.${index}.modifier`, modifier);
    setValue(`keybinds.${index}.display_key`, finalKey.toUpperCase());
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    event.preventDefault();
  };

  const handleReset = (index: number, defaultValue: SavedKeybind) => {
    setValue(`keybinds.${index}.key`, defaultValue.key);
    const defaultValueDisplayKey = defaultValue.modifier
      ? `${defaultValue.modifier}-${defaultValue.key}`.toUpperCase()
      : defaultValue.key.toUpperCase();
    setValue(`keybinds.${index}.display_key`, defaultValueDisplayKey);
  };

  const handleClear = (index: number) => {
    setValue(`keybinds.${index}.key`, "");
    setValue(`keybinds.${index}.display_key`, "");
  };

  return (
    <Flex
      direction="column"
      gap={7}
      height="full"
      width="full"
      style={{ marginTop: "auto" }}
    >
      <Flex align="center" gap={6}>
        <Heading size="md">Keybinds</Heading>
        <Heading size="md">
          {selected_rotation.game} - {selected_rotation.class} -{" "}
          {selected_rotation.name}
        </Heading>
      </Flex>
      {selected_rotation.name === "hekili" ? (
        <Heading size="sm">
          Hekili does not support custom keybinds yet.
        </Heading>
      ) : (
        <form
          style={{
            width: "100%",
            height: "100%",
            position: "relative",
            display: "flex",
            flexDirection: "column",
          }}
          onSubmit={handleSubmit(onSubmit)}
        >
          <Box
            flex="1"
            overflowY="auto"
            paddingBottom="80px" // Ensures the last row is visible above the button
          >
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>Spell</Th>
                  <Th textAlign="center">Keybind</Th>
                </Tr>
              </Thead>
              <Tbody>
                {keybinds.map((keybind, index) => (
                  <Tr key={`${keybind.name}-${selected_rotation.name}`}>
                    <Td>{getName(keybind.name)}</Td>
                    <Td>
                      <Flex align="center" justify="flex-end" gap={2}>
                        <Controller
                          name={`keybinds.${index}.display_key`}
                          control={control}
                          defaultValue={keybind.key}
                          render={({ field }) => (
                            <Input
                              {...field}
                              width="auto"
                              onFocus={(e) =>
                                (e.target.placeholder = activeModifier
                                  ? `${activeModifier}-`
                                  : "Press a key")
                              }
                              onBlur={(e) => (e.target.placeholder = "")}
                              onKeyUp={(e) => handleKeyUp(e, index)}
                              onKeyDown={handleKeyDown}
                            />
                          )}
                        />
                        <input type="hidden" name={`keybinds.${index}.key`} />
                        <Button onClick={() => handleReset(index, keybind)}>
                          Revert
                        </Button>
                        <Button onClick={() => handleClear(index)}>
                          Clear
                        </Button>
                      </Flex>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </Box>

          <Box
            position="fixed"
            bottom="0px"
            left="0px"
            width={"98%"}
            backgroundColor={"gray.800"}
            padding="10px"
            borderRadius="md"
          >
            <Button
              className="btn-settings"
              _hover={{
                backgroundPosition: "right center",
                textDecoration: "none",
              }}
              type="submit"
            >
              Save keybinds
            </Button>
          </Box>
        </form>
      )}
    </Flex>
  );
};
