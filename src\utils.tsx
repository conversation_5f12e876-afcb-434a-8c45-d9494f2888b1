import { ConfigInput, ConfigType } from "./types";

export const is_wow_path_addons = (path?: string) =>
  path && path.toLowerCase().endsWith("addons");

export const config_invalid = (config?: ConfigInput) => {
  if (!config) {
    return true;
  }

  if (!config.wow_path || !is_wow_path_addons(config.wow_path)) {
    return true;
  }

  if (!config.selected_rotation) {
    return true;
  }

  if (
    config.selected_rotation &&
    (!config.selected_rotation.name ||
      !config.selected_rotation.class ||
      !config.selected_rotation.game)
  ) {
    return true;
  }

  return false;
};

export const getClasses = (game?: string) => {
  switch (game) {
    case "retail":
      return [
        "DeathKnight",
        "DemonHunter",
        "Evoker",
        "Monk",
        "Druid",
        "Hunter",
        "Mage",
        "Paladin",
        "Priest",
        "Rogue",
        "Shaman",
        "Warlock",
        "Warrior",
      ];
    case "classic":
      return [
        "Druid",
        "Hunter",
        "Mage",
        "Palad<PERSON>",
        "Priest",
        "Rogue",
        "Shaman",
        "Warlock",
        "Warrior",
        "DeathKnight",
      ];
    case "sod":
      return [
        "Druid",
        "Hunter",
        "Mage",
        "Paladin",
        "Priest",
        "Rogue",
        "Shaman",
        "Warlock",
        "Warrior",
      ];
    case "era":
      return [
        "Druid",
        "Hunter",
        "Mage",
        "Paladin",
        "Priest",
        "Rogue",
        "Shaman",
        "Warlock",
        "Warrior",
      ];
    default:
      return [];
  }
};

export const getSpecs = (game: string, class_: string) => {
  if (game === "retail") {
    switch (class_) {
      case "DeathKnight":
        return ["Blood", "Frost", "Unholy"];
      case "DemonHunter":
        return ["Havoc", "Vengeance"];
      case "Evoker":
        return ["Devastation", "Preservation", "Augmentation"];
      case "Druid":
        return ["Balance", "Feral", "Guardian", "Restoration"];
      case "Hunter":
        return ["Beast Mastery", "Marksmanship", "Survival"];
      case "Mage":
        return ["Arcane", "Fire", "Frost"];
      case "Monk":
        return ["Brewmaster", "Mistweaver", "Windwalker"];
      case "Paladin":
        return ["Holy", "Protection", "Retribution"];
      case "Priest":
        return ["Discipline", "Holy", "Shadow"];
      case "Rogue":
        return ["Assassination", "Outlaw", "Subtlety"];
      case "Shaman":
        return ["Elemental", "Enhancement", "Restoration"];
      case "Warlock":
        return ["Affliction", "Demonology", "Destruction"];
      case "Warrior":
        return ["Arms", "Fury", "Protection"];
      default:
        return [];
    }
  } else if (game === "classic" || game === "era" || game === "sod") {
    switch (class_) {
      case "Druid":
        return ["Balance", "Feral", "Restoration"];
      case "Hunter":
        return ["Beast Mastery", "Marksmanship", "Survival"];
      case "Mage":
        return ["Arcane", "Fire", "Frost"];
      case "Paladin":
        return ["Holy", "Protection", "Retribution"];
      case "Priest":
        return ["Discipline", "Holy", "Shadow"];
      case "Rogue":
        return ["Assassination", "Combat", "Subtlety"];
      case "Shaman":
        return ["Elemental", "Enhancement", "Restoration"];
      case "Warlock":
        return ["Affliction", "Demonology", "Destruction"];
      case "Warrior":
        return ["Arms", "Fury", "Protection"];
      case "DeathKnight":
        return ["Blood", "Frost", "Unholy"];
      default:
        return [];
    }
  }
  return [];
};

export const DEFAULT_CONFIG: ConfigType = {
  wow_path: "",
  addon_name: "OptiStrike",
  activation_key: "",
  selected_rotation: {
    name: "",
    class: "",
    game: "",
    spec: "",
    author: "",
    version: "",
    spells: [],
    macros: [],
    original_name: "",
  },
  rotations: [],
  rate_limit: 60,
  key_limit: 10,
};
