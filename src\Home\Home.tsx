import { useEffect, useRef, useState } from "react";
import { invoke } from "@tauri-apps/api/tauri";
import { emit, listen } from "@tauri-apps/api/event";
import {
  Box,
  Button,
  Divider,
  Flex,
  Heading,
  Text,
  useColorMode,
} from "@chakra-ui/react";
import "./Home.css";
import { Config } from "./Config";
import { config_invalid } from "../utils";
import { useSettingsStore } from "../stores/store";

type StatusPayload = {
  status: string;
  running?: boolean;
};

type Pixel = {
  name: string;
  press: boolean;
  key: string | null;
  r: number;
  g: number;
  b: number;
};

function rgbToHex(r: number, g: number, b: number) {
  return "#" + ((1 << 24) | (r << 16) | (g << 8) | b).toString(16).slice(1);
}

export const Home = () => {
  const { colorMode, toggleColorMode } = useColorMode();
  const [error, setError] = useState<string | undefined>();
  const [status, setStatus] = useState<StatusPayload>({
    status: "OptiStrike Initialized!",
  });
  const [running, setRunning] = useState<boolean>(false);
  const [statusLog, setStatusLog] = useState<string[]>([]);
  const { settings } = useSettingsStore();
  const [data, setData] = useState<Pixel[]>();

  async function start() {
    const error: string = await invoke("start_bot");
    setError(error);
  }

  function stop() {
    emit("stop", {
      running: false,
    });
  }

  useEffect(() => {
    let unsubscribe: any;

    const registerListener = async () => {
      unsubscribe = await listen(
        "status",
        ({ payload }: { payload: StatusPayload }) => {
          setStatus({ status: payload.status });
          if (payload.running === true || payload.running === false) {
            setRunning(payload.running);
          }
        }
      );
    };

    registerListener();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  useEffect(() => {
    let unsubscribe: any;

    const registerListener = async () => {
      unsubscribe = await listen("data", (event) => {
        setData(event.payload as Pixel[]);
      });
    };

    registerListener();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  useEffect(() => {
    if (colorMode === "light") {
      toggleColorMode();
    }
  }, []);

  const oldStatusRef = useRef<StatusPayload>();

  useEffect(() => {
    oldStatusRef.current = status;
  }, [status]);

  useEffect(() => {
    setStatusLog((prev) => {
      const isSame = prev[prev.length - 1] === status.status;
      const updatedLog = isSame ? prev : [...prev, status.status];
      // take last 30 entries
      return updatedLog.slice(Math.max(updatedLog.length - 10, 0));
    });
  }, [status]);

  const botEnabled = !config_invalid(settings);
  const shouldPress = data?.filter((p) => p.press && p.key) || [];
  const rotationName = settings?.selected_rotation?.name ?? "";

  return (
    <Flex direction="row" gap={2} height={"full"} position={"relative"}>
      <Flex direction="column" width="full">
        <Config />
      </Flex>
      <Divider orientation="vertical" />
      <Flex direction="column" width="full" justifyContent="space-between">
        <Box>
          <Box
            bg="black"
            color="white"
            padding={2}
            borderRadius="md"
            borderWidth={1}
            borderStyle="solid"
            borderColor="gray.600"
            display="inline-block"
            width="full"
            height="209px"
          >
            {statusLog.slice(0, statusLog.length - 1).map((log, i) => (
              <Text fontFamily="monospace" key={i} fontSize="sm">
                {log}
              </Text>
            ))}
          </Box>
          <Box
            bg="black"
            color="white"
            padding={2}
            borderRadius="md"
            borderWidth={1}
            borderStyle="solid"
            borderColor="gray.600"
            display="inline-block"
            width="full"
          >
            <Text fontFamily="monospace" fontSize="sm">
              {status.status}
            </Text>
          </Box>

          {shouldPress.length > 0 && running && (
            <Box py={6}>
              <Heading size="sm">Action que</Heading>
              <Flex mt="12px" wrap="wrap" gap="15px">
                {shouldPress.map(({ name, r, b, g }) => (
                  <Box key={name} width="125px">
                    <Text style={{ color: rgbToHex(r, g, b) }}>{name}</Text>
                  </Box>
                ))}
              </Flex>
            </Box>
          )}

          {/*           {data && (
            <Box py={6}>
              <Heading size="sm">Debug pixels</Heading>
              <Flex mt="12px" wrap="wrap" gap="15px">
                {data.map(({ name, r, g, b }) => (
                  <Box key={name} width="125px">
                    <Text style={{ color: rgbToHex(r, g, b) }}>{name}</Text>
                  </Box>
                ))}
              </Flex>
            </Box>
          )} */}

          <Box mt={4}>
            {rotationName && (
              <Text>
                Current rotation: <b>{rotationName}</b>
              </Text>
            )}
            {botEnabled && running && (
              <Button
                size="lg"
                width="full"
                color="white"
                className="btn-err"
                _hover={{
                  backgroundPosition: "right center",
                  textDecoration: "none",
                }}
                mt={2}
                onClick={stop}
              >
                Stop
              </Button>
            )}
            {botEnabled && !running && (
              <Button
                width="full"
                size="lg"
                disabled={!!error}
                className="btn-grad"
                _hover={{
                  backgroundPosition: "right center",
                  textDecoration: "none",
                }}
                mt={2}
                onClick={start}
              >
                Start
              </Button>
            )}
            {!botEnabled && (
              <Button
                size="lg"
                width="full"
                color="white"
                backgroundColor="gray.900"
                isDisabled
                mt={2}
              >
                Check config
              </Button>
            )}
          </Box>
        </Box>
      </Flex>
    </Flex>
  );
};
