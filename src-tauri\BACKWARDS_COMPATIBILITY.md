# WoW Rotation System - Backwards Compatibility

This document explains how the rotation system maintains compatibility across all World of Warcraft versions.

## Supported Versions

✅ **Classic Era** (Vanilla 1.15.x)  
✅ **Cataclysm Classic** (4.4.x)  
✅ **Retail WoW** (11.x+)

## API Differences Between Versions

### Spell Functions

| Function | Classic Era | Retail WoW |
|----------|-------------|------------|
| Get spell info | `GetSpellInfo(spellId)` | `C_Spell.GetSpellInfo(spellId)` |
| Get cooldown | `GetSpellCooldown(spellId)` | `C_Spell.GetSpellCooldown(spellId)` |
| Check usability | `IsUsableSpell(spellId)` | `C_Spell.IsSpellUsable(spellId)` |
| Check if known | `IsSpellKnown(spellId)` | `C_Spell.IsSpellKnown(spellId)` |
| Get charges | `GetSpellCharges(spellId)` | `C_Spell.GetSpellCharges(spellId)` |
| Check range | `IsSpellInRange(spellId, unit)` | `C_Spell.IsSpellInRange(spellId, unit)` |

### Aura Functions

| Function | Classic Era | Retail WoW |
|----------|-------------|------------|
| Get buff | `UnitBuff(unit, index)` | `C_UnitAuras.GetBuffDataByIndex(unit, index)` |
| Get debuff | `UnitDebuff(unit, index)` | `C_UnitAuras.GetDebuffDataByIndex(unit, index)` |
| Find aura | Manual iteration | `AuraUtil.FindAuraByName(name, unit, filter)` |

## Backwards Compatibility Implementation

### Pattern Used

Following the pattern from `os.lua`, we use this approach:

```lua
local FunctionName = _G.FunctionName or function(...)
    if ModernNamespace and ModernNamespace.FunctionName then
        return ModernNamespace.FunctionName(...)
    end
    return fallback_value
end
```

### Example Implementation

```lua
-- Backwards compatible GetSpellInfo
local GetSpellInfo = _G.GetSpellInfo or function(spellId)
    if C_Spell and C_Spell.GetSpellInfo then
        local spellInfo = C_Spell.GetSpellInfo(spellId)
        if spellInfo then
            return spellInfo.name, nil, spellInfo.iconID, spellInfo.castTime,
                   spellInfo.minRange, spellInfo.maxRange, spellInfo.spellID
        end
    end
    return nil
end
```

### Aura Compatibility

Classic doesn't have `AuraUtil.FindAuraByName`, so we provide a fallback:

```lua
local function FindAuraByName(auraName, unit, filter)
    if AuraUtil and AuraUtil.FindAuraByName then
        return AuraUtil.FindAuraByName(auraName, unit, filter)
    end
    
    -- Classic fallback using UnitBuff/UnitDebuff
    local isHelpful = filter == "HELPFUL"
    local func = isHelpful and UnitBuff or UnitDebuff
    
    for i = 1, 40 do
        local name, icon, count, dispelType, duration, expirationTime, 
              source, isStealable, nameplateShowPersonal, spellId = func(unit, i)
        if not name then break end
        
        if name == auraName then
            return {
                name = name,
                icon = icon,
                applications = count or 1,
                dispelName = dispelType,
                duration = duration,
                expirationTime = expirationTime,
                sourceUnit = source,
                isStealable = isStealable,
                nameplateShowPersonal = nameplateShowPersonal,
                spellId = spellId
            }
        end
    end
    
    return nil
end
```

## Version Detection

The system automatically detects the WoW version by checking for the existence of modern namespaces:

```lua
-- If C_Spell exists, we're on Retail
if C_Spell and C_Spell.GetSpellInfo then
    -- Use Retail API
else
    -- Use Classic API
end
```

## Testing Across Versions

### Classic Era Testing
- Test with Vanilla spells (e.g., Mortal Strike ID: 12294)
- Verify buff/debuff detection works with UnitBuff/UnitDebuff
- Ensure resource detection works with UnitPower

### Retail Testing  
- Test with modern spell IDs
- Verify C_Spell namespace functions work
- Test AuraUtil.FindAuraByName functionality

## Common Gotchas

### 1. Spell IDs
Different versions may have different spell IDs for the same spell. Always verify spell IDs for each version.

### 2. Return Values
Some functions return different data structures:
- Classic: Simple return values
- Retail: Table structures with named fields

### 3. Aura Limits
- Classic: Maximum 40 buffs/debuffs per unit
- Retail: Higher limits, but uses different iteration methods

## Generated Code Compatibility

All generated Lua code includes backwards compatibility headers:

```lua
-- Generated Custom Rotation: Example Rotation
-- Compatible with Classic Era, Cataclysm Classic, and Retail WoW

-- Include rotation objects
-- Rotation objects should be loaded from rotation_objects.lua
-- This provides Player, Target, Spell, and RotationHelper objects with backwards compatibility
```

## Maintenance

When WoW APIs change:

1. **Check for new namespaces** in patch notes
2. **Update compatibility functions** in `rotation_objects.lua`
3. **Test on all supported versions**
4. **Update documentation** with new patterns

## Performance Considerations

The backwards compatibility layer has minimal performance impact:
- Functions are resolved once at load time
- No runtime version checking in hot paths
- Fallbacks only used when modern APIs don't exist

## Future Versions

When new WoW versions are released:

1. **Test existing compatibility** - often no changes needed
2. **Add new API support** if functions are moved/changed
3. **Update version list** in documentation
4. **Consider deprecating** very old versions if maintenance becomes difficult

This approach ensures that rotations work seamlessly across all WoW versions without requiring separate codebases or user configuration.
