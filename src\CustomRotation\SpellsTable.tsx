import {
  <PERSON>,
  <PERSON>ing,
  But<PERSON>,
  <PERSON>,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Input,
} from "@chakra-ui/react";
import { useFormContext, useFieldArray } from "react-hook-form";
import { useState, useEffect } from "react";
import { CustomRotation, CustomRotationSpell } from "../types";

interface SpellsTableProps {
  size?: "sm" | "md" | "lg";
  compact?: boolean;
}

export const SpellsTable = ({
  size = "md",
  compact = false,
}: SpellsTableProps) => {
  const { register, watch, setValue, control } =
    useFormContext<CustomRotation>();

  const {
    fields: spells,
    append: appendSpell,
    remove: removeSpell,
  } = useFieldArray({
    control,
    name: "spells",
  });

  const watchSpells = watch("spells");
  const controlledSpells = spells.map((spell, index) => ({
    ...spell,
    ...watchSpells[index],
  }));

  // Local state to track input values for each spell
  const [inputValues, setInputValues] = useState<{ [key: string]: string }>({});

  // Initialize input values when spells change
  useEffect(() => {
    const newInputValues: { [key: string]: string } = {};
    controlledSpells.forEach((spell) => {
      if (!inputValues[spell.id]) {
        newInputValues[spell.id] = spell.spell_ids.join(",");
      }
    });
    if (Object.keys(newInputValues).length > 0) {
      setInputValues((prev) => ({ ...prev, ...newInputValues }));
    }
  }, [controlledSpells.length]);

  const handleSpellIdsChange = (
    spellId: string,
    index: number,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value;

    // Format the input in real-time: replace spaces with commas
    const formattedValue = value.replace(/\s+/g, ",");

    // Update local state to maintain input value
    setInputValues((prev) => ({ ...prev, [spellId]: formattedValue }));

    // Parse and clean the IDs for form state
    const cleanedIds = formattedValue
      // Split by comma
      .split(",")
      // Remove empty strings and trim
      .map((id: string) => id.trim())
      .filter((id: string) => id !== "")
      // Convert to numbers and filter out invalid ones
      .map((id: string) => {
        const parsedId = parseInt(id);
        return isNaN(parsedId) ? 0 : parsedId;
      })
      .filter((id: number) => id !== 0);

    setValue(`spells.${index}.spell_ids`, cleanedIds);
  };

  return (
    <Box>
      <Heading size={compact ? "sm" : "md"} mb={4}>
        Spells
      </Heading>
      <Button
        onClick={() =>
          appendSpell({
            id: crypto.randomUUID(),
            name: "",
            spell_ids: [],
          })
        }
        mb={4}
        size={compact ? "sm" : "md"}
      >
        Add Spell
      </Button>
      <Table variant="simple" size={size}>
        <Thead>
          <Tr>
            <Th>Spell Name</Th>
            <Th>Spell IDs</Th>
            <Th width={compact ? "30px" : "50px"}></Th>
          </Tr>
        </Thead>
        <Tbody>
          {controlledSpells?.map(
            (spell: CustomRotationSpell, index: number) => (
              <Tr key={spell.id}>
                <Td>
                  <Input
                    {...register(`spells.${index}.name`)}
                    placeholder="Spell name"
                    size={compact ? "sm" : "md"}
                  />
                </Td>
                <Td>
                  <Input
                    value={inputValues[spell.id] || ""}
                    onChange={(e) => handleSpellIdsChange(spell.id, index, e)}
                    placeholder="Comma-separated spell IDs"
                    size={compact ? "sm" : "md"}
                  />
                </Td>
                <Td>
                  <Button
                    size={compact ? "xs" : "sm"}
                    colorScheme="red"
                    variant="ghost"
                    onClick={() => removeSpell(index)}
                  >
                    ×
                  </Button>
                </Td>
              </Tr>
            )
          )}
        </Tbody>
      </Table>
    </Box>
  );
};
