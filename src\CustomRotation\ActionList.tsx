import {
  Box,
  Button,
  HStack,
  IconButton,
  Input,
  Select,
  VStack,
  Text,
  Tooltip,
  useColorModeValue,
  Heading,
  Flex,
} from "@chakra-ui/react";
import {
  DeleteIcon,
  EditIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  PlusSquareIcon,
} from "@chakra-ui/icons";
import { ConditionGroup, CustomRotation } from "../types";
import { ConditionGroup as ConditionGroupComponent } from "./ConditionGroup";
import { useFieldArray, useFormContext } from "react-hook-form";

export const ActionList = ({ sectionId }: { sectionId: string }) => {
  const { control, register, watch, setValue } =
    useFormContext<CustomRotation>();

  const sections = watch("sections") || [];
  const spells = watch("spells") || [];
  const sectionIndex = sections.findIndex((s) => s.id === sectionId);
  const section = sections[sectionIndex];

  // Always call useFieldArray, but use a safe path when section is not found
  const { remove: removeAction, swap: swapAction } = useFieldArray({
    control,
    name:
      sectionIndex !== -1
        ? `sections.${sectionIndex}.actions`
        : "sections.0.actions",
  });

  if (!section || sectionIndex === -1) {
    return null;
  }

  const addConditionToAction = (actionId: string) => {
    const newGroup: ConditionGroup = {
      id: crypto.randomUUID(),
      logic: "AND",
      conditions: [],
      entityType: "GROUP",
      propertyType: "CONDITION",
    };

    const action = section.actions.find((a) => a.id === actionId);
    if (!action) {
      console.error("Action not found");
      return;
    }

    const updatedAction = {
      ...action,
      conditions: [...action.conditions, newGroup],
    };

    setValue(
      `sections.${sectionIndex}.actions`,
      section.actions.map((a) => (a.id === actionId ? updatedAction : a))
    );
  };

  const borderColor = useColorModeValue("gray.200", "gray.700");
  const bgColor = useColorModeValue("white", "gray.800");

  return (
    <VStack spacing={4} align="stretch">
      {section.actions.map((action, index) => (
        <Box
          key={action.id}
          borderRadius="lg"
          p="2px"
          bgGradient="linear(135deg, rgba(79, 172, 254, 0.6) 0%, rgba(0, 242, 254, 0.6) 50%, rgba(79, 172, 254, 0.6) 100%)"
          shadow="sm"
        >
          <Box borderRadius="lg" bg={bgColor} p={4}>
            {/* Action Header */}
            <HStack justify="space-between" mb={4}>
              <HStack spacing={4} flex={1}>
                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={1}>
                    Action Type
                  </Text>
                  <Select
                    {...register(
                      `sections.${sectionIndex}.actions.${index}.action_type`
                    )}
                    size="sm"
                    maxW="150px"
                  >
                    <option value="CAST">Cast</option>
                    <option value="WAIT">Wait</option>
                    <option value="START_SECTION">Start Section</option>
                  </Select>
                </Box>

                <Box flex={1}>
                  <Text fontSize="sm" fontWeight="medium" mb={1}>
                    Value
                  </Text>
                  {action.action_type === "START_SECTION" && (
                    <Select
                      {...register(
                        `sections.${sectionIndex}.actions.${index}.startSectionId`
                      )}
                      size="sm"
                    >
                      {sections
                        .filter((s) => s.id !== section.id)
                        .map((section) => (
                          <option key={section.id} value={section.id}>
                            {section.name}
                          </option>
                        ))}
                    </Select>
                  )}
                  {action.action_type === "CAST" && (
                    <Select
                      {...register(
                        `sections.${sectionIndex}.actions.${index}.spellId`
                      )}
                      size="sm"
                    >
                      {spells.map((spell) => (
                        <option key={spell.name} value={spell.id}>
                          {spell.name}
                        </option>
                      ))}
                    </Select>
                  )}
                  {action.action_type === "WAIT" && (
                    <Input
                      type="number"
                      {...register(
                        `sections.${sectionIndex}.actions.${index}.waitSeconds`
                      )}
                      size="sm"
                      placeholder="Seconds"
                      width="100%"
                    />
                  )}
                </Box>
              </HStack>

              {/* Action Controls */}
              <HStack>
                <Tooltip label="Add condition group" placement="top">
                  <IconButton
                    aria-label="Add condition group"
                    icon={<PlusSquareIcon />}
                    onClick={() => addConditionToAction(action.id)}
                    size="sm"
                    colorScheme="green"
                    variant="ghost"
                  />
                </Tooltip>
                <IconButton
                  aria-label="Move action up"
                  icon={<ChevronUpIcon />}
                  onClick={() => swapAction(index, index - 1)}
                  isDisabled={index === 0}
                  size="sm"
                  variant="ghost"
                />
                <IconButton
                  aria-label="Move action down"
                  icon={<ChevronDownIcon />}
                  onClick={() => swapAction(index, index + 1)}
                  isDisabled={index === section.actions.length - 1}
                  size="sm"
                  variant="ghost"
                />
                <IconButton
                  aria-label="Delete action"
                  icon={<DeleteIcon />}
                  onClick={() => removeAction(index)}
                  colorScheme="red"
                  size="sm"
                  variant="ghost"
                />
              </HStack>
            </HStack>

            {/* Conditions Section */}
            <Box>
              <Text fontSize="sm" fontWeight="medium" mb={2} color="gray.600">
                Conditions
              </Text>
              <VStack align="stretch" spacing={2}>
                {action.conditions.map((condition, conditionIndex) => (
                  <Box key={condition.id}>
                    {"conditions" in condition ? (
                      <ConditionGroupComponent
                        name={`sections.${sectionIndex}.actions.${index}.conditions`}
                        index={conditionIndex}
                        groupId={condition.id}
                      />
                    ) : (
                      <HStack
                        p={3}
                        borderWidth="1px"
                        borderRadius="md"
                        justify="space-between"
                        bg={useColorModeValue("gray.50", "gray.700")}
                      >
                        <Text fontSize="sm">
                          {condition.entityType} {condition.propertyType}{" "}
                          {condition.operator} {condition.value}
                          {condition.propertyType === "HEALTH" && "%"}
                          {condition.propertyType === "RESOURCE" && "%"}
                          {condition.propertyType === "COOLDOWN" && "s"}
                          {condition.propertyType === "DEBUFF_REMAINING" && "s"}
                        </Text>
                        <HStack>
                          <IconButton
                            aria-label="Edit condition"
                            icon={<EditIcon />}
                            onClick={() => {}}
                            size="sm"
                            variant="ghost"
                          />
                          <IconButton
                            aria-label="Delete condition"
                            icon={<DeleteIcon />}
                            onClick={() => {}}
                            colorScheme="red"
                            size="sm"
                            variant="ghost"
                          />
                        </HStack>
                      </HStack>
                    )}
                  </Box>
                ))}

                {action.conditions.length === 0 && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      addConditionToAction(action.id);
                    }}
                  >
                    Add Condition Group
                  </Button>
                )}
              </VStack>
            </Box>
          </Box>
        </Box>
      ))}
    </VStack>
  );
};
