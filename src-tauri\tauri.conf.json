{"build": {"beforeDevCommand": "yarn dev:front", "beforeBuildCommand": "yarn build:front", "devPath": "http://localhost:1420", "distDir": "../dist", "withGlobalTauri": false}, "package": {"productName": "rukc3xd", "version": "1.2.3"}, "tauri": {"allowlist": {"all": false, "dialog": {"open": true}, "shell": {"all": false, "open": true}, "protocol": {"asset": true, "assetScope": ["$APPCACHE/**", "$APPLOCALDATA/**", "$RESOURCE/**", "$EXECUTABLE/**"]}, "fs": {"all": true, "scope": ["**"]}}, "bundle": {"active": true, "targets": "all", "identifier": "com.opti.strike", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["addon_files/*", "libs/*", "LogitechDriver.exe"], "windows": {"allowDowngrades": true, "certificateThumbprint": null, "digestAlgorithm": null, "nsis": null, "timestampUrl": null, "tsp": false, "webviewFixedRuntimePath": null, "webviewInstallMode": {"silent": true, "type": "downloadBootstrapper"}, "wix": null}}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "visible": true, "label": "main", "title": "Lwkdu2x", "width": 800, "height": 640}]}}