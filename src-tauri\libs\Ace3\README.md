Ace3 - AddOn development framework
==================================

Ace3 is a comprehensive framework for WoW AddOn development to streamline many of the common tasks in developing addons. This includes lifecycles, saved variables, configuration, event handling, network communications, and more.

Documentation
-------------

A basic introduction to usage can be found here:  
https://www.wowace.com/projects/ace3/pages/getting-started

Documentation can be found here:  
https://www.wowace.com/addons/ace3/pages/

Resources
---------
A development repository is available on GitHub:  
https://github.com/WoWUIDev/Ace3

Please note that the CurseForge/WoWAce SVN repository remains the primary authority, and what should be used in .pkgmeta to reference Ace3.

.pkgmeta reference:  
Base Path: https://repos.wowace.com/wow/ace3/trunk  
Example for AceAddon-3.0: https://repos.wowace.com/wow/ace3/trunk/AceAddon-3.0

It is recommended to reference the specific Ace3 libraries you are using directly, instead of pulling in the entire package.
