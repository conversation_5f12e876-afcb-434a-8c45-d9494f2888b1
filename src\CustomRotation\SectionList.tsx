import {
  Box,
  Button,
  HStack,
  Input,
  VStack,
  Icon<PERSON>utton,
  Text,
} from "@chakra-ui/react";
import { ChevronUpIcon, ChevronDownIcon, DeleteIcon } from "@chakra-ui/icons";
import { ActionList } from "./ActionList";
import { useFieldArray, useFormContext } from "react-hook-form";
import {
  CustomRotation,
  CustomRotationAction,
  CustomRotationSection,
} from "../types";

export const SectionList = () => {
  const { control, register, watch, setValue } =
    useFormContext<CustomRotation>();
  const {
    fields: sectionsFieldArray,
    append: appendSection,
    remove: removeSection,
    swap: swapSection,
  } = useFieldArray({
    control,
    name: "sections",
  });

  const watchSections = watch("sections");
  const sections = sectionsFieldArray.map((section, index) => ({
    ...section,
    ...watchSections[index],
  }));

  const handleAddSection = () => {
    appendSection({
      id: crypto.randomUUID(),
      name: `Section ${sections.length + 1}`,
      order: sections.length + 1,
      actions: [],
    });
  };

  const handleAddActionToSection = (section: CustomRotationSection) => {
    const newAction: CustomRotationAction = {
      id: crypto.randomUUID(),
      order: sections.length + 1,
      action_type: "CAST",
      conditions: [],
      sectionId: section.id,
      spellId: null,
      waitSeconds: null,
    };
    const updatedSection = {
      ...section,
      actions: [...section.actions, newAction],
    };

    console.log(updatedSection);

    setValue(
      `sections.${sections.findIndex((s) => s.id === section.id)}`,
      updatedSection
    );
  };

  return (
    <VStack align="stretch" spacing={4}>
      {sections
        .sort((a, b) => a.order - b.order)
        .map((section, index) => (
          <Box
            key={section.id}
            borderRadius="lg"
            p="2px"
            bgGradient="linear(to-r, rgba(218, 34, 255, 0.6) 0%, rgba(151, 51, 238, 0.6) 51%, rgba(218, 34, 255, 0.6) 100%)"
          >
            <Box borderRadius="lg" p={4} bg="gray.800">
              <VStack alignItems="start" mb={4}>
                <Text fontSize="sm" fontWeight="medium" mb={1}>
                  Section
                </Text>
                <HStack width="100%">
                  <Input
                    {...register(`sections.${index}.name`)}
                    placeholder="Section Name"
                    width="100%"
                  />
                  <IconButton
                    aria-label="Move section up"
                    icon={<ChevronUpIcon />}
                    onClick={() => swapSection(index, index - 1)}
                    isDisabled={section.order === 1}
                    size="sm"
                  />
                  <IconButton
                    aria-label="Move section down"
                    icon={<ChevronDownIcon />}
                    onClick={() => swapSection(index, index + 1)}
                    isDisabled={section.order === sections.length}
                    size="sm"
                  />
                  <IconButton
                    aria-label="Delete section"
                    icon={<DeleteIcon />}
                    onClick={() => removeSection(index)}
                    colorScheme="red"
                    size="sm"
                  />
                </HStack>
              </VStack>
              <ActionList sectionId={section.id} />
              <Button
                mt={4}
                colorScheme="blue"
                onClick={() => handleAddActionToSection(section)}
              >
                Add Action
              </Button>
            </Box>
          </Box>
        ))}

      <Button width={"full"} onClick={handleAddSection} colorScheme="blue">
        Add Section
      </Button>
    </VStack>
  );
};
