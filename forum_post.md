[FONT="Lucida Sans Unicode"]Hey ePvP! Long time lurker here, finally something to release :handsdown:

[SIZE="6"][URL="https://optistrike.pro"][COLOR="DarkOrchid"]OptiStrike rotations[/COLOR][/URL][/SIZE]

A new pixel based rotation bot for World of Warcraft

Currently looking for [B]rotation developers[/B] & testers.
The bot is still in [B]really early stages[/B] and there is only test rotation for wotlk fire mage.
I'll keep you updated on the status! Currently at least classic mage & paladin is being developed & I will be importing Hekili to retail

[B]If you know LUA or you're willing to learn, I challenge you to create the best parsing rotations:cool:![/B]

Current features:
[LIST]
[*]Autokeybind
[*]OPTI API
[*]No lua unlocker needed
[*]Support for every wow version
[*]...many more to come!
[/LIST]

If you are interested and want to know more you can join our [URL="https://discord.gg/UXP3a5rCjA"][SIZE="3"][COLOR="DeepSkyBlue"]Discord[/COLOR][/SIZE][/URL]

While we are in early access, bot is obviously free.
Use the activation code: [B]EA_dev-248be[/B]

[B]Download[/B]
[URL="https://www.mediafire.com/file/colfeq3m0e9obwq/optistrike_0.0.3_x64_en-US.msi/file"]v0.0.3 download[/URL]

[URL="https://www.virustotal.com/gui/file/****************************************************************/detection"]VirusTotal[/URL]

[B]For testers:[/B]
Use the [B]classic/mage/OS_TEST_Fire[/B] rotation

[B]For rotation developers[/B]
[URL="https://discord.gg/UXP3a5rCjA"]Join our discord[/URL] for API documentation and get started guide.

If the app crashes, make sure you're running as admin (needs permissions to copy addon files)

You can ask questions here, dm me or in our discord!

[IMG=expandable: 1, title: Screenshot]https://www.optistrike.pro/images/screenshot.png[/IMG][/FONT]