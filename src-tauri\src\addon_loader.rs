use crate::{log, log_error};
use std::fs;
use std::io;
use std::path::Path;
use walkdir::WalkDir;

static LOCAL_FOLDERS_TO_CLEAN: [&str; 6] = [
    "rotations",
    "classic-hero",
    "retail-hero",
    "hero_addon",
    "hekili-classic",
    "hekili-retail",
];

pub const WOW_ADDONS_TO_CLEAR: [&str; 5] =
    ["herorotation", "herolib", "herodbc", "herocache", "hekili"];

pub fn clean_local_addon_files() -> io::Result<()> {
    for folder in LOCAL_FOLDERS_TO_CLEAN.iter() {
        if let Err(err) = delete_data(folder) {
            log_error(&format!("Error deleting data: {}", err));
        }
    }

    log("Local addon files cleaned");

    Ok(())
}

pub fn clean_wow_addons(wow_path: &str, addon_name: &str, new_name: &str) -> io::Result<()> {
    let wow_path = Path::new(wow_path);
    log("Cleaning old addons..");
    if let Ok(entries) = fs::read_dir(wow_path) {
        for entry in entries.filter_map(|e| e.ok()) {
            if let Ok(file_type) = entry.file_type() {
                if file_type.is_dir() {
                    let folder_name = entry.file_name();
                    if let Some(name) = folder_name.to_str() {
                        let name_lower = name.to_lowercase();
                        if WOW_ADDONS_TO_CLEAR.contains(&name_lower.as_str())
                            || name_lower == addon_name.to_lowercase()
                            || name_lower == new_name.to_lowercase()
                        {
                            if let Err(err) = fs::remove_dir_all(entry.path()) {
                                log_error(&format!("Error deleting addon {}: {}", name, err));
                            } else {
                                log(&format!("Deleted addon: {}", name));
                            }
                        }
                    }
                }
            }
        }
    }

    Ok(())
}

pub fn delete_data(folder: &str) -> io::Result<()> {
    let folder_path = Path::new(folder);

    for entry in WalkDir::new(folder_path).into_iter().filter_map(|e| e.ok()) {
        let path = entry.path();
        if path.is_file() {
            println!("Cleaning file: {:?}", path);
            fs::remove_file(path)?;
        } else if path.is_dir() {
            println!("Cleaning directory: {:?}", path);
            fs::remove_dir_all(path)?;
        }
    }

    Ok(())
}
