name: CI

permissions:
  contents: read
on:
  workflow_dispatch:
  pull_request:
    branches:
      - main

env:
  CARGO_TERM_COLOR: always

jobs:
  ci:
    strategy:
      fail-fast: false
      matrix:
        rust:
          - stable
          - nightly
          - "1.75.0"
        platform:
          - ubuntu-latest
          - windows-latest
          - macos-latest
        features:
          - "--all-features"
          - "--features default"
          - "--features libei"
          - "--features wayland"
          - "--features xdo"
          - "--features x11rb"
        exclude:
          - platform: windows-latest
            features: "--features libei"
          - platform: windows-latest
            features: "--features wayland"
          - platform: windows-latest
            features: "--features xdo"
          - platform: windows-latest
            features: "--features x11rb"
          - platform: macos-latest
            features: "--features libei"
          - platform: macos-latest
            features: "--features wayland"
          - platform: macos-latest
            features: "--features xdo"
          - platform: macos-latest
            features: "--features x11rb"
    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/install_deps
      - uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ matrix.rust }}
          components: rustfmt, clippy

      - name: Check the code format
        if: matrix.rust == 'nightly' # Avoid differences between the versions
        run: cargo fmt -- --check

      - name: Check clippy lints
        run: cargo clippy --no-default-features ${{ matrix.features }} -- -D clippy::pedantic
      - name: Check clippy lints for the examples
        run: cargo clippy --no-default-features ${{ matrix.features }} --examples -- -D clippy::pedantic

      - name: Build the code
        run: cargo build --no-default-features ${{ matrix.features }}

      - name: Build the docs
        run: cargo doc --no-deps --no-default-features ${{ matrix.features }}

      - name: Build the examples
        run: cargo check --examples --no-default-features ${{ matrix.features }}

      - name: Build the examples in release mode
        run: cargo check --release --examples --no-default-features ${{ matrix.features }}

      - name: Test the code
        run: cargo test --no-default-features ${{ matrix.features }} -- --nocapture

      - name: Test the code in release mode
        run: cargo test --release --no-default-features ${{ matrix.features }} -- --nocapture

      - name: Setup headless display for integration tests
        if: runner.os == 'Linux' # The integration tests only work on Linux right now
        uses: ./.github/actions/headless_display
      - name: Run integration tests
        if: runner.os == 'Linux' && matrix.features == 'xdo' # The integration tests only work on Linux and x11 right now
        run: cargo test --no-default-features ${{ matrix.features }} -- --include-ignored --nocapture

      - name: Install Miri
        if: matrix.rust == 'nightly' # Miri only works on Nightly
        run: |
          rustup toolchain install nightly --component miri
          rustup override set nightly
          cargo clean
          cargo miri setup
      - name: Run Miri to check unsafe code
        if: matrix.rust == 'nightly' # Miri only works on Nightly
        run: cargo miri test --verbose --no-default-features ${{ matrix.features }}