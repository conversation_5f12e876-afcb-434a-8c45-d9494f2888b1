# Lua Rotation Script Setup Guide


### --Important: Do Not Modify!--

Ensure the section containing  `OPTI.mapKeybinds(OPTI.spells)`  remains unchanged. It's critical for mapping your defined spells to their in-game actions.
The rotation must have a section with

```lua
-- DO NOT REMOVE
OPTI.spells = {
    -- Spells here
}

OPTI.mapKeybinds(OPTI.spells)
-- DO NOT REMOVE ^
```

## Spell and Item Definition

**OPTI.spells** section is used to define and bind spells and items needed for the rotation.

```lua
OPTI.spells = {
    { spell = "SPELL YourSpellName", name = "YourSpellName" },
    { spell = "ITEM YourItemName", name = "YourItemName" },
    -- Define additional spells and items here
}
```
**spell**: Use the `SetBinding()` command for binding. For more info, check [Wowpedia’s SetBinding](https://wowpedia.fandom.com/wiki/API_SetBinding). For example  `SPELL Fireball` or `ITEM Mana Gem`.
**name**: The name that will be displayed in logs for tracking.

## Events

Use the  `OnUpdate`  function for continuously checking and executing based on the defined priority logic.

```lua
local function OnUpdate(self, event)
    OPTI.resetPixels() -- Reset all pixels
    
    if ShouldCastFireball() then
        OPTI.cast('Fireball')
    end
end

frame:SetScript("OnUpdate", OnUpdate)
```
## Casting

Use `OPTI.cast(spellName)` to tell the app to cast a spell
You can use `OPTI.resetPixels()` to clear the status of all spells

--
