use std::fs;
use std::io;
use std::path::Path;

use crate::constants::DRY_CORE_FILE_NAME;

pub fn copy_dir_all(src: impl AsRef<Path>, dst: impl AsRef<Path>) -> io::Result<()> {
    fs::create_dir_all(&dst)?;
    // Iterate over the entries in the source directory
    for entry in fs::read_dir(src.as_ref())? {
        let entry = entry?;
        let path = entry.path();
        let dest_path = dst.as_ref().join(entry.file_name());

        if path.is_dir() {
            // Recursively copy subdirectories
            copy_dir_all(&path, &dest_path)?;
        } else {
            fs::copy(&path, &dest_path)?;
        }
    }
    Ok(())
}

pub fn copy_addon_folder_to_wow(src: impl AsRef<Path>, dst: impl AsRef<Path>) -> io::Result<()> {
    fs::create_dir_all(&dst)?;
    // Iterate over the entries in the source directory
    for entry in fs::read_dir(src.as_ref())? {
        let entry = entry?;
        let path = entry.path();
        let dest_path = dst.as_ref().join(entry.file_name());

        if path.is_dir() {
            // Recursively copy subdirectories
            copy_addon_folder_to_wow(&path, &dest_path)?;
        } else {
            let file_name = path.file_name().unwrap().to_str().unwrap();
            // Copy files
            if file_name.ends_with(".toml") || file_name == DRY_CORE_FILE_NAME {
                continue;
            }

            fs::copy(&path, &dest_path)?;
        }
    }
    Ok(())
}
