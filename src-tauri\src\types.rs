use serde::{Deserialize, Serialize};

fn default_custom_type() -> String {
    "CUSTOM".to_string()
}

#[derive(Clone, Serialize)]
pub struct StatusPayload {
    pub status: String,
    pub running: bool,
}

#[derive(Clone, Serialize)]
pub struct OptionalPayload {
    pub status: String,
    pub running: Option<bool>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct RotationConfigSpell {
    pub name: String,
    pub spell_ids: Vec<u32>,
    pub pixel: Option<i32>,
    pub stopcasting: Option<bool>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct RotationConfigMacro {
    pub content: String,
    pub spell_ids: Vec<u32>,
    pub name: String,
    pub pixel: Option<i32>,
    pub stopcasting: Option<bool>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct KeybindPixel {
    pub name: String,
    pub key: Option<String>,
    pub pixel: i32,
    pub key_code: i32,
    pub modifier: Option<String>,
}

#[derive(Debug, Deserialize, Serialize, <PERSON>lone)]
pub struct RotationConfig {
    pub name: String,
    pub game: String,
    pub class: String,
    pub spec: String,
    pub author: String,
    pub version: String,
    pub spells: Vec<RotationConfigSpell>,
    #[serde(default)]
    pub macros: Vec<RotationConfigMacro>,
    pub dependencies: Option<Vec<String>>,
    pub load_order: Vec<String>,
    pub original_name: Option<String>,
    pub is_custom: Option<bool>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct RotationWithKeybinds {
    pub config: RotationConfig,
    pub keybinds: Vec<SavedKeybind>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct Rotation {
    pub name: String,
    pub class: String,
    pub game: String,
    pub dependencies: Option<Vec<String>>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct Settings {
    pub wow_path: String,
    pub addon_name: String,
    pub activation_key: Option<String>,
    pub selected_rotation: RotationConfig,
    pub rotations: Vec<RotationConfig>,
    pub rate_limit: u32,
    pub key_limit: u32,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct ConfigInput {
    pub wow_path: String,
    pub selected_rotation: RotationConfig,
}

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq, Eq, Hash)]
pub struct Keybind {
    pub key: String,
    pub key_code: i32,
    pub pixel: i32,
    pub name: String,
    pub spell: String,
    pub spell_ids: Vec<u32>,
}

#[derive(serde::Deserialize, Debug, Clone)]
pub struct SelectedRotation {
    pub class: String,
    pub game: String,
    pub rotation: String,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct RotationKeybinds {
    pub rotation: String,
    pub class: String,
    pub game: String,
    pub keybinds: Vec<Keybind>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct SavedKeybind {
    pub name: String,
    pub key: String,
    pub key_code: i32,
    pub modifier: Option<String>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct SavedRotationKeybinds {
    pub rotation_id: String,
    pub keybinds: Vec<SavedKeybind>,
}

pub struct Interface<'a> {
    pub name: &'a str,
    pub version: u32,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct Pixel {
    pub name: String,
    pub key: Option<String>,
    pub key_code: i32,
    pub r: u8,
    pub g: u8,
    pub b: u8,
    pub x: u32,
    pub should_press: bool,
    pub modifier: Option<String>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct CustomRotationSpell {
    pub id: String,
    pub name: String,
    pub spell_ids: Vec<u32>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct CustomRotationBuff {
    pub id: String,
    pub name: String,
    pub spell_ids: Vec<u32>,
    pub type_: String,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct CustomRotationCondition {
    pub id: String,
    pub entityType: String,
    #[serde(default)]
    pub propertyType: Option<String>,
    #[serde(default)]
    pub operator: Option<String>,
    #[serde(default)]
    pub value: Option<serde_json::Value>,
    #[serde(default)]
    pub valueType: Option<String>,
    #[serde(default)]
    pub logic: Option<String>,
    #[serde(default)]
    pub conditions: Option<Vec<CustomRotationCondition>>,
    #[serde(default)]
    pub requiredFields: Option<std::collections::HashMap<String, String>>,
    #[serde(default)]
    pub groupId: Option<String>,
    #[serde(default)]
    pub checkSpellId: Option<String>,
    #[serde(default)]
    pub buffId: Option<String>,
    #[serde(default)]
    pub debuffId: Option<String>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct CustomRotationAction {
    pub id: String,
    pub action_type: String,
    pub order: u32,
    pub conditions: Vec<CustomRotationCondition>,
    pub spellId: Option<String>,
    pub waitSeconds: Option<f32>,
    pub startSectionId: Option<String>,
    #[serde(default)]
    pub sectionId: Option<String>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct CustomRotationSection {
    pub id: String,
    pub name: String,
    pub order: u32,
    pub actions: Vec<CustomRotationAction>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct CustomRotation {
    pub id: String,
    pub name: String,
    pub class: String,
    pub game: String,
    pub spec: String,
    pub author: String,
    pub version: String,
    pub sections: Vec<CustomRotationSection>,
    pub spells: Vec<CustomRotationSpell>,
    pub buffs: Vec<CustomRotationBuff>,
    pub macros: Option<Vec<RotationConfigMacro>>,
    pub is_custom: bool,
    #[serde(rename = "type", default = "default_custom_type")]
    pub type_: String,
}
