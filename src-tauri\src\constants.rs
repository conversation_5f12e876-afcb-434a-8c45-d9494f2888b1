use crate::types::Interface;

pub const ROTATIONS_PATH: &str = "rotations";
pub const CUSTOM_ROTATIONS_PATH: &str = "custom_rotations";
pub const SETTINGS_PATH: &str = "settings.json";
pub const KEYBINDS_PATH: &str = "keybinds.json";
pub const ADDON_FOLDER_TO_COPY_PATH: &str = "addon_files";
pub const CORE_FILE_NAME: &str = "os.lua";
pub const DRY_CORE_FILE_NAME: &str = "dry_core.lua";

pub static INTERFACES: &[Interface] = &[
    Interface {
        name: "Era",
        version: 11501,
    },
    Interface {
        name: "SoD",
        version: 11501,
    },
    Interface {
        name: "Classic",
        version: 40400,
    },
    Interface {
        name: "Retail",
        version: 110005,
    },
];
