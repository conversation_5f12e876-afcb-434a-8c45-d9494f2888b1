# Testing Setup

This project uses Jest with React Testing Library for unit testing.

## Setup

The testing environment is configured with:

- **Jest**: Test runner and framework
- **@testing-library/react**: React component testing utilities
- **@testing-library/jest-dom**: Custom Jest matchers for DOM elements
- **@testing-library/user-event**: User interaction simulation
- **ts-jest**: TypeScript support for Jest
- **jest-environment-jsdom**: DOM environment for testing React components

## Configuration

- **Jest config**: `jest.config.ts`
- **Test setup**: `src/setupTests.ts`
- **TypeScript config**: Updated `tsconfig.json` with `esModuleInterop: true`

## Available Scripts

```bash
# Run all tests
yarn test

# Run tests in watch mode
yarn test:watch

# Run tests with coverage
yarn test:coverage

# Run tests for CI (no watch, with coverage)
yarn test:ci

# Run specific test files
yarn test src/__tests__/setup.test.ts
```

## Test Structure

Tests are organized in the following structure:

```
src/
├── __tests__/           # General utility tests
│   ├── setup.test.ts    # Basic Jest setup verification
│   ├── utils.test.ts    # Utility function tests
│   └── types.test.ts    # Type schema validation tests
└── CustomRotation/
    └── __tests__/       # Component-specific tests
        ├── ConditionModal.test.tsx
        └── ConditionGroup.test.tsx
```

## Mocks

The test setup includes mocks for:

- **Tauri API**: All `@tauri-apps/api` modules are mocked
- **crypto.randomUUID**: Generates test UUIDs with `test-uuid-` prefix
- **window.matchMedia**: Media query matching
- **ResizeObserver**: DOM resize observation
- **IntersectionObserver**: DOM intersection observation

## Writing Tests

### Component Tests

```typescript
import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ChakraProvider } from '@chakra-ui/react';
import { MyComponent } from '../MyComponent';

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ChakraProvider>
    {children}
  </ChakraProvider>
);

describe('MyComponent', () => {
  it('renders correctly', () => {
    render(
      <TestWrapper>
        <MyComponent />
      </TestWrapper>
    );
    
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
});
```

### Utility Tests

```typescript
describe('Utility Function', () => {
  it('should format strings correctly', () => {
    expect(formatString('test_string')).toBe('Test String');
  });
});
```

## Coverage

Coverage reports are generated in the `coverage/` directory when running:

```bash
yarn test:coverage
```

## Best Practices

1. **Use descriptive test names** that explain what is being tested
2. **Group related tests** using `describe` blocks
3. **Mock external dependencies** to isolate units under test
4. **Test user interactions** rather than implementation details
5. **Use `screen` queries** from Testing Library for better accessibility
6. **Clean up after tests** using `beforeEach` and `afterEach` hooks

## Troubleshooting

### Common Issues

1. **Import errors**: Make sure `esModuleInterop: true` is set in `tsconfig.json`
2. **Chakra UI components not rendering**: Wrap components in `ChakraProvider`
3. **Form components failing**: Wrap in `FormProvider` from `react-hook-form`
4. **Tauri API errors**: All Tauri modules are mocked in `setupTests.ts`

### Debug Tips

- Use `screen.debug()` to see the current DOM state
- Use `--verbose` flag to see individual test results
- Check the console for any unhandled promise rejections or warnings
