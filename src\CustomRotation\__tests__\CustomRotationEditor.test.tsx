import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { ChakraProvider } from "@chakra-ui/react";
import { CustomRotationEditor } from "../CustomRotationEditor";
import { useCustomRotationStore } from "../../stores/customRotationStore";
import { CustomRotation } from "../../types";
import { invoke } from "@tauri-apps/api/tauri";

// Mock the store
jest.mock("../../stores/customRotationStore");
const mockUseCustomRotationStore =
  useCustomRotationStore as jest.MockedFunction<typeof useCustomRotationStore>;

// Mock Tauri invoke
const mockInvoke = invoke as jest.MockedFunction<typeof invoke>;

// Mock child components
jest.mock("../SectionList", () => ({
  SectionList: () => <div data-testid="section-list">Section List</div>,
}));

jest.mock("../BasicInfoSection", () => ({
  BasicInfoSection: () => (
    <div data-testid="basic-info-section">Basic Info Section</div>
  ),
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ChakraProvider>{children}</ChakraProvider>
);

const mockRotations: CustomRotation[] = [
  {
    id: "rotation-1",
    name: "Test Rotation 1",
    class: "Warrior",
    game: "WoW",
    spec: "Arms",
    author: "Test Author",
    version: "1.0.0",
    sections: [],
    spells: [{ id: "spell-1", name: "Mortal Strike", spell_ids: [12294] }],
    buffs: [
      { id: "buff-1", name: "Battle Shout", spell_ids: [6673], type_: "BUFF" },
    ],
    is_custom: true,
    type: "CUSTOM",
  },
  {
    id: "rotation-2",
    name: "Test Rotation 2",
    class: "Mage",
    game: "WoW",
    spec: "Fire",
    author: "Test Author 2",
    version: "1.0.0",
    sections: [],
    spells: [],
    buffs: [],
    is_custom: true,
    type: "CUSTOM",
  },
];

const defaultMockStore = {
  customRotations: [],
  setCustomRotations: jest.fn(),
  selectedRotation: null,
  setSelectedRotation: jest.fn(),
  formState: {
    isOpen: false,
    setIsOpen: jest.fn(),
    isLoading: false,
    setIsLoading: jest.fn(),
    isEditing: false,
    setIsEditing: jest.fn(),
  },
};

describe("CustomRotationEditor", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseCustomRotationStore.mockReturnValue(defaultMockStore as any);
    mockInvoke.mockResolvedValue([]);
  });

  it("renders the component with initial state", () => {
    render(
      <TestWrapper>
        <CustomRotationEditor />
      </TestWrapper>
    );

    expect(screen.getByText("New Rotation")).toBeInTheDocument();
    expect(screen.getByText("Saved Rotations")).toBeInTheDocument();
  });

  describe("Loading rotations", () => {
    it("loads rotations on component mount", async () => {
      mockInvoke.mockResolvedValue(mockRotations);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith("load_custom_rotations");
      });

      expect(defaultMockStore.formState.setIsLoading).toHaveBeenCalledWith(
        true
      );
      expect(defaultMockStore.setCustomRotations).toHaveBeenCalledWith(
        mockRotations
      );
    });

    it("handles loading error gracefully", async () => {
      const errorMessage = "Failed to load rotations";
      mockInvoke.mockRejectedValue(new Error(errorMessage));

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith("load_custom_rotations");
      });

      expect(defaultMockStore.formState.setIsLoading).toHaveBeenCalledWith(
        false
      );
    });
  });

  describe("Selecting existing rotation", () => {
    it("displays saved rotations and allows selection", () => {
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        customRotations: mockRotations,
      } as any);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      expect(screen.getByText("Test Rotation 1")).toBeInTheDocument();
      expect(screen.getByText("Test Rotation 2")).toBeInTheDocument();
    });

    it("selects a rotation when clicked", async () => {
      const user = userEvent.setup();
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        customRotations: mockRotations,
      } as any);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const rotationButton = screen.getByText("Test Rotation 1");
      await user.click(rotationButton);

      expect(defaultMockStore.setSelectedRotation).toHaveBeenCalledWith(
        mockRotations[0]
      );
      expect(defaultMockStore.formState.setIsOpen).toHaveBeenCalledWith(true);
      expect(defaultMockStore.formState.setIsEditing).toHaveBeenCalledWith(
        true
      );
    });

    it("highlights selected rotation", () => {
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        customRotations: mockRotations,
        selectedRotation: mockRotations[0],
      } as any);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const selectedButton = screen.getByText("Test Rotation 1");
      expect(selectedButton).toHaveClass("chakra-button");
    });
  });

  describe("Creating new rotation", () => {
    it("creates a new rotation when New Rotation button is clicked", async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const newRotationButton = screen.getByText("New Rotation");
      await user.click(newRotationButton);

      expect(defaultMockStore.setSelectedRotation).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.any(String),
          name: "",
          class: "",
          game: "",
          spec: "",
          author: "",
          version: "1.0.0",
          sections: [],
          spells: [],
          buffs: [],
          is_custom: true,
          type: "CUSTOM",
        })
      );
      expect(defaultMockStore.formState.setIsOpen).toHaveBeenCalledWith(true);
      expect(defaultMockStore.formState.setIsEditing).toHaveBeenCalledWith(
        false
      );
    });
  });

  describe("Editing rotation form", () => {
    beforeEach(() => {
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: mockRotations[0],
      } as any);
    });

    it("displays the form when isOpen is true", () => {
      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      expect(screen.getByTestId("basic-info-section")).toBeInTheDocument();
      expect(screen.getByText("Spells")).toBeInTheDocument();
      expect(screen.getByText("Buffs/Debuffs")).toBeInTheDocument();
      expect(screen.getByText("Sections")).toBeInTheDocument();
      expect(screen.getByTestId("section-list")).toBeInTheDocument();
    });

    it("displays form action buttons", () => {
      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      expect(screen.getByText("Save Rotation")).toBeInTheDocument();
      expect(screen.getByText("Delete")).toBeInTheDocument();
      expect(screen.getByText("Cancel")).toBeInTheDocument();
    });
  });

  describe("Adding and managing spells", () => {
    beforeEach(() => {
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: {
          ...mockRotations[0],
          spells: [],
        },
      } as any);
    });

    it("displays Add Spell button", () => {
      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      expect(screen.getByText("Add Spell")).toBeInTheDocument();
    });

    it("displays spell table headers", () => {
      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      expect(screen.getByText("Spell Name")).toBeInTheDocument();
      expect(screen.getAllByText("Spell IDs")).toHaveLength(2); // One for spells, one for buffs
    });

    it("displays existing spells in the table", () => {
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: mockRotations[0],
      } as any);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      expect(screen.getByDisplayValue("Mortal Strike")).toBeInTheDocument();
      expect(screen.getByDisplayValue("12294")).toBeInTheDocument();
    });

    it("allows adding a new spell", async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const addSpellButton = screen.getByText("Add Spell");
      await user.click(addSpellButton);

      // Should add a new spell row with empty inputs
      const spellNameInputs = screen.getAllByPlaceholderText("Spell name");
      const spellIdInputs = screen.getAllByPlaceholderText(
        "Comma-separated spell IDs"
      );

      expect(spellNameInputs).toHaveLength(1);
      expect(spellIdInputs).toHaveLength(2); // One for spells, one for buffs (from default rotation)
    });

    it("allows editing spell name", async () => {
      const user = userEvent.setup();
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: mockRotations[0],
      } as any);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const spellNameInput = screen.getByDisplayValue("Mortal Strike");
      await user.clear(spellNameInput);
      await user.type(spellNameInput, "Updated Spell Name");

      expect(spellNameInput).toHaveValue("Updated Spell Name");
    });

    it("allows editing spell IDs", async () => {
      const user = userEvent.setup();
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: mockRotations[0],
      } as any);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const spellIdInput = screen.getByDisplayValue("12294");
      await user.clear(spellIdInput);
      await user.type(spellIdInput, "12294,12295");

      expect(spellIdInput).toHaveValue("12294,12295");
    });

    it("allows removing spells", async () => {
      const user = userEvent.setup();
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: mockRotations[0],
      } as any);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const removeButtons = screen.getAllByText("×");
      const spellRemoveButton = removeButtons[0]; // First remove button is for spells
      await user.click(spellRemoveButton);

      // The spell should be removed from the form
      expect(
        screen.queryByDisplayValue("Mortal Strike")
      ).not.toBeInTheDocument();
    });
  });

  describe("Adding and managing buffs/debuffs", () => {
    beforeEach(() => {
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: {
          ...mockRotations[0],
          buffs: [],
        },
      } as any);
    });

    it("displays Add Buff/Debuff button", () => {
      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      expect(screen.getByText("Add Buff/Debuff")).toBeInTheDocument();
    });

    it("displays buff table headers", () => {
      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      expect(screen.getByText("Name")).toBeInTheDocument();
      expect(screen.getByText("Type")).toBeInTheDocument();
      expect(screen.getAllByText("Spell IDs")).toHaveLength(2); // One for spells, one for buffs
    });

    it("displays existing buffs in the table", () => {
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: mockRotations[0],
      } as any);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      expect(screen.getByDisplayValue("Battle Shout")).toBeInTheDocument();
      expect(screen.getByDisplayValue("6673")).toBeInTheDocument();
    });

    it("allows adding a new buff/debuff", async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const addBuffButton = screen.getByText("Add Buff/Debuff");
      await user.click(addBuffButton);

      // Should add a new buff row with empty inputs
      const buffNameInputs = screen.getAllByPlaceholderText("Buff/Debuff name");

      expect(buffNameInputs).toHaveLength(1);
    });

    it("allows selecting buff type", async () => {
      const user = userEvent.setup();
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: mockRotations[0],
      } as any);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const typeSelect = screen.getByDisplayValue("Buff");
      await user.selectOptions(typeSelect, "DEBUFF");

      expect(screen.getByDisplayValue("Debuff")).toBeInTheDocument();
    });

    it("allows removing buffs", async () => {
      const user = userEvent.setup();
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: mockRotations[0],
      } as any);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      // Find the remove button in the buffs section (second × button)
      const removeButtons = screen.getAllByText("×");
      const buffRemoveButton = removeButtons[1]; // Second remove button is for buffs
      await user.click(buffRemoveButton);

      // The buff should be removed from the form
      expect(
        screen.queryByDisplayValue("Battle Shout")
      ).not.toBeInTheDocument();
    });
  });

  describe("Saving rotation", () => {
    beforeEach(() => {
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: mockRotations[0],
      } as any);
    });

    it("saves rotation when Save Rotation button is clicked", async () => {
      const user = userEvent.setup();
      mockInvoke.mockResolvedValueOnce(undefined); // save_custom_rotation
      mockInvoke.mockResolvedValueOnce(mockRotations); // load_custom_rotations

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const saveButton = screen.getByText("Save Rotation");
      await user.click(saveButton);

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith("save_custom_rotation", {
          rotation: expect.objectContaining({
            type: "CUSTOM",
          }),
        });
      });

      expect(defaultMockStore.formState.setIsOpen).toHaveBeenCalledWith(false);
    });

    it("handles save error gracefully", async () => {
      const user = userEvent.setup();
      const errorMessage = "Failed to save rotation";
      mockInvoke.mockRejectedValue(new Error(errorMessage));

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const saveButton = screen.getByText("Save Rotation");
      await user.click(saveButton);

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith(
          "save_custom_rotation",
          expect.any(Object)
        );
      });

      expect(defaultMockStore.formState.setIsLoading).toHaveBeenCalledWith(
        false
      );
    });

    it("reloads rotations after successful save", async () => {
      const user = userEvent.setup();
      // First call is the initial load on mount, second is save, third is reload after save, fourth is get_current_rotations
      mockInvoke.mockResolvedValueOnce([]); // initial load_custom_rotations
      mockInvoke.mockResolvedValueOnce(undefined); // save_custom_rotation
      mockInvoke.mockResolvedValueOnce(mockRotations); // load_custom_rotations after save
      mockInvoke.mockResolvedValueOnce([]); // get_current_rotations

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const saveButton = screen.getByText("Save Rotation");
      await user.click(saveButton);

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith(
          "save_custom_rotation",
          expect.any(Object)
        );
      });

      // Wait for all calls to complete
      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledTimes(4); // initial load + save + reload + get_current_rotations
      });

      expect(defaultMockStore.setCustomRotations).toHaveBeenCalledWith(
        mockRotations
      );
    });
  });

  describe("Deleting rotation", () => {
    beforeEach(() => {
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: mockRotations[0],
      } as any);
    });

    it("deletes rotation when Delete button is clicked", async () => {
      const user = userEvent.setup();
      mockInvoke.mockResolvedValueOnce(undefined); // delete_custom_rotation
      mockInvoke.mockResolvedValueOnce(mockRotations); // load_custom_rotations

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const deleteButton = screen.getByText("Delete");
      await user.click(deleteButton);

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith("delete_custom_rotation", {
          id: mockRotations[0].id,
        });
      });

      expect(defaultMockStore.setSelectedRotation).toHaveBeenCalledWith(null);
      expect(defaultMockStore.formState.setIsOpen).toHaveBeenCalledWith(false);
      expect(defaultMockStore.formState.setIsEditing).toHaveBeenCalledWith(
        false
      );
    });

    it("disables delete button when no rotation is selected", () => {
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: null,
      } as any);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const deleteButton = screen.getByText("Delete");
      expect(deleteButton).toBeDisabled();
    });

    it("reloads rotations after successful delete", async () => {
      const user = userEvent.setup();
      mockInvoke.mockResolvedValueOnce(undefined); // delete_custom_rotation
      mockInvoke.mockResolvedValueOnce([]); // load_custom_rotations (empty after delete)

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const deleteButton = screen.getByText("Delete");
      await user.click(deleteButton);

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith("load_custom_rotations");
      });

      expect(defaultMockStore.setCustomRotations).toHaveBeenCalledWith([]);
    });
  });

  describe("Form actions", () => {
    beforeEach(() => {
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: true,
        },
        selectedRotation: mockRotations[0],
      } as any);
    });

    it("closes form when Cancel button is clicked", async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      const cancelButton = screen.getByText("Cancel");
      await user.click(cancelButton);

      expect(defaultMockStore.formState.setIsOpen).toHaveBeenCalledWith(false);
    });

    it("does not display form when isOpen is false", () => {
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isOpen: false,
        },
        selectedRotation: mockRotations[0],
      } as any);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      expect(
        screen.queryByTestId("basic-info-section")
      ).not.toBeInTheDocument();
      expect(screen.queryByText("Save Rotation")).not.toBeInTheDocument();
    });
  });

  describe("Loading states", () => {
    it("shows loading state during operations", () => {
      mockUseCustomRotationStore.mockReturnValue({
        ...defaultMockStore,
        formState: {
          ...defaultMockStore.formState,
          isLoading: true,
        },
      } as any);

      render(
        <TestWrapper>
          <CustomRotationEditor />
        </TestWrapper>
      );

      // Component should handle loading state appropriately
      expect(screen.getByText("New Rotation")).toBeInTheDocument();
    });
  });
});
