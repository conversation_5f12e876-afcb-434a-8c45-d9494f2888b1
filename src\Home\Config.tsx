import {
  Box,
  Button,
  Flex,
  <PERSON><PERSON>abel,
  Heading,
  Input,
  Select,
  Text,
} from "@chakra-ui/react";
import { open } from "@tauri-apps/api/dialog";
import "./Config.css";
import {
  config_invalid,
  DEFAULT_CONFIG,
  getClasses,
  is_wow_path_addons,
} from "../utils";
import { ConfigInput, ConfigType } from "../types";
import { invoke } from "@tauri-apps/api";
import { useSettingsStore } from "../stores/store";
import { Controller, useForm } from "react-hook-form";
import { useEffect } from "react";

export const Config = () => {
  const {
    settings: config,
    setSettings,
    setSelectedRotation,
  } = useSettingsStore();

  const settings = config ?? DEFAULT_CONFIG;

  const { control, handleSubmit, setValue, watch } = useForm<ConfigInput>({
    defaultValues: {
      wow_path: settings.wow_path ?? "",
      selected_rotation:
        settings.selected_rotation ?? DEFAULT_CONFIG.selected_rotation,
    },
  });

  useEffect(() => {
    setValue("wow_path", settings.wow_path ?? "");
    setValue(
      "selected_rotation",
      settings.selected_rotation ?? DEFAULT_CONFIG.selected_rotation
    );
  }, [settings]);

  useEffect(() => {
    setSelectedRotation(watch("selected_rotation"));
  }, [watch("selected_rotation.name")]);

  const saveSettings = async (data: ConfigInput) => {
    const rotation = settings.rotations.find(
      (r) =>
        r.name === data.selected_rotation.name &&
        r.class === data.selected_rotation.class &&
        r.game === data.selected_rotation.game
    );
    if (!rotation) {
      console.log("Rotation not found");
      return;
    }
    data.selected_rotation = rotation;
    const jsonStr = JSON.stringify(data);
    const updated_settings = await invoke<ConfigType>("save_settings", {
      payload: jsonStr,
    });

    setSettings(updated_settings);
  };

  const handler = async () => {
    const _folderPath = await open({ directory: true, multiple: false });
    const path =
      (Array.isArray(_folderPath) ? _folderPath[0] : _folderPath) ?? "";
    setValue("wow_path", path);
  };

  // Check if wow_path ends with AddOns
  const invalid = config_invalid(watch());

  const games = ["Era", "SoD", "Classic", "Retail"];
  const game =
    watch("selected_rotation.game") ??
    settings.selected_rotation.game.toLowerCase();
  const classes = getClasses(game);

  const selectedClass =
    watch("selected_rotation.class") ?? settings.selected_rotation?.class ?? "";

  const availableRotations = settings.rotations.filter(
    (r) =>
      r.class.toLowerCase() === selectedClass?.toLowerCase() &&
      game.toLowerCase() === r.game.toLowerCase()
  );

  return (
    <Box pb={6}>
      <Heading size="sm">Config</Heading>
      <Flex mt="12px" wrap="wrap" gap="12px">
        <form style={{ width: "100%" }} onSubmit={handleSubmit(saveSettings)}>
          <Box width="full">
            <FormLabel>WoW Addon folder</FormLabel>
            <Flex direction="row" gridGap="10px" width="full">
              <Controller
                name={"wow_path"}
                control={control}
                defaultValue={settings.wow_path}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Your wow Addon folder"
                    name="wow_path"
                  />
                )}
              />
              <Button px={8} onClick={handler}>
                Select Folder
              </Button>
            </Flex>

            {!is_wow_path_addons(watch("wow_path")) && (
              <Text color="red.500">Please select AddOns folder</Text>
            )}
          </Box>

          <Box width="full">
            <FormLabel>Select version</FormLabel>
            <Flex direction="row" gridGap="10px" width="full">
              <Controller
                name={"selected_rotation.game"}
                control={control}
                defaultValue={settings.selected_rotation.game}
                render={({ field }) => (
                  <Select
                    {...field}
                    name="wow_version"
                    placeholder="Select version"
                  >
                    {games.map((g) => (
                      <option key={g.toLowerCase()} value={g.toLowerCase()}>
                        {g}
                      </option>
                    ))}
                  </Select>
                )}
              />
            </Flex>
          </Box>

          <Box width="full">
            <FormLabel>Select class</FormLabel>
            <Flex direction="row" gridGap="10px" width="full">
              <Controller
                name={"selected_rotation.class"}
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    name="selected_class"
                    placeholder="Select class"
                  >
                    {classes.map((wowclass, i) => (
                      <option
                        key={`${wowclass.toLowerCase()}-${i}`}
                        value={wowclass.toLowerCase()}
                      >
                        {wowclass}
                      </option>
                    ))}
                  </Select>
                )}
              />
            </Flex>
          </Box>

          <Box width="full">
            <FormLabel>Select rotation</FormLabel>
            <Flex direction="row" gridGap="10px" width="full">
              <Controller
                name={"selected_rotation.name"}
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    name="selected_rotation"
                    placeholder="Select rotation"
                  >
                    {availableRotations?.map((rotation) => (
                      <option
                        key={rotation.name}
                        value={rotation.name.toLowerCase()}
                      >
                        {rotation.original_name}
                      </option>
                    ))}
                  </Select>
                )}
              />
            </Flex>
          </Box>

          <Box width="full">
            <Button
              type="submit"
              className="btn-settings"
              _hover={{
                backgroundPosition: "right center",
                textDecoration: "none",
              }}
              mt="15px"
              isDisabled={invalid}
            >
              Load rotation
            </Button>
          </Box>
        </form>
      </Flex>
    </Box>
  );
};
