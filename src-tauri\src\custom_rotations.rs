use crate::constants::CUSTOM_ROTATIONS_PATH;
use crate::keybinds;
use crate::log;
use crate::types::{CustomRotation, RotationConfig, RotationConfigMacro, RotationConfigSpell};
use std::fs::{self, File};
use std::io::Write;

const CUSTOM_ROTATIONS_JSON_PATH: &str = "custom_rotations/priorities";

/// Convert CustomRotation to RotationConfig for the global rotations list
fn custom_rotation_to_rotation_config(custom_rotation: &CustomRotation) -> RotationConfig {
    // Convert CustomRotationSpell to RotationConfigSpell
    let spells: Vec<RotationConfigSpell> = custom_rotation
        .spells
        .iter()
        .enumerate()
        .map(|(index, spell)| RotationConfigSpell {
            name: spell.name.clone(),
            spell_ids: spell.spell_ids.clone(),
            pixel: Some(index as i32 + 50), // Assign pixel values based on order
            stopcasting: None,
        })
        .collect();

    // Convert macros if they exist
    let macros: Vec<RotationConfigMacro> = custom_rotation
        .macros
        .as_ref()
        .unwrap_or(&Vec::new())
        .iter()
        .map(|macro_| RotationConfigMacro {
            content: macro_.content.clone(),
            spell_ids: macro_.spell_ids.clone(),
            name: macro_.name.clone(),
            pixel: macro_.pixel,
            stopcasting: macro_.stopcasting,
        })
        .collect();

    RotationConfig {
        name: custom_rotation.name.clone(),
        game: custom_rotation.game.clone(),
        class: custom_rotation.class.clone(),
        spec: custom_rotation.spec.clone(),
        author: custom_rotation.author.clone(),
        version: custom_rotation.version.clone(),
        spells,
        macros,
        dependencies: None, // Custom rotations don't have dependencies
        load_order: vec!["Priorities.lua".to_string()],
        original_name: Some(custom_rotation.name.clone()),
        is_custom: Some(true),
    }
}

/// Update the global rotations list with custom rotations
pub fn update_global_rotations_list() -> Result<(), String> {
    // Load all custom rotations
    let custom_rotations = load_custom_rotations_sync()?;

    // Convert to RotationConfig
    let custom_rotation_configs: Vec<RotationConfig> = custom_rotations
        .iter()
        .map(custom_rotation_to_rotation_config)
        .collect();

    // Call the main.rs function to update the global list
    crate::update_rotations_with_custom(custom_rotation_configs);

    Ok(())
}

/// Synchronous version of load_custom_rotations for internal use
fn load_custom_rotations_sync() -> Result<Vec<CustomRotation>, String> {
    let mut rotations = Vec::new();

    // Create the directory if it doesn't exist
    if !std::path::Path::new(CUSTOM_ROTATIONS_JSON_PATH).exists() {
        fs::create_dir_all(CUSTOM_ROTATIONS_JSON_PATH).map_err(|e| e.to_string())?;
        return Ok(rotations); // Return empty vector if directory was just created
    }

    // Read all files in the custom_rotations/priorities directory
    let entries = fs::read_dir(CUSTOM_ROTATIONS_JSON_PATH).map_err(|e| e.to_string())?;

    for entry in entries {
        if let Ok(entry) = entry {
            if let Some(extension) = entry.path().extension() {
                if extension == "json" {
                    let contents = fs::read_to_string(entry.path()).map_err(|e| e.to_string())?;
                    println!("Contents: {}", contents);
                    let rotation: CustomRotation =
                        serde_json::from_str(&contents).map_err(|e| e.to_string())?;

                    rotations.push(rotation);
                }
            }
        }
    }

    Ok(rotations)
}

#[tauri::command]
pub async fn save_custom_rotation(rotation: CustomRotation) -> Result<(), String> {
    // Create custom_rotations/priorities directory if it doesn't exist
    fs::create_dir_all(CUSTOM_ROTATIONS_JSON_PATH).map_err(|e| e.to_string())?;

    // Check if this is an existing rotation being renamed
    let mut old_file_path: Option<String> = None;

    // Look for existing rotation with the same ID but different name
    let entries = fs::read_dir(CUSTOM_ROTATIONS_JSON_PATH).map_err(|e| e.to_string())?;
    for entry in entries {
        if let Ok(entry) = entry {
            if let Some(extension) = entry.path().extension() {
                if extension == "json" {
                    let contents = fs::read_to_string(entry.path()).map_err(|e| e.to_string())?;
                    if let Ok(existing_rotation) = serde_json::from_str::<CustomRotation>(&contents)
                    {
                        if existing_rotation.id == rotation.id {
                            // Found existing rotation with same ID
                            let existing_filename = entry
                                .path()
                                .file_stem()
                                .and_then(|s| s.to_str())
                                .unwrap_or("")
                                .to_string();
                            let new_filename = rotation.name.replace(" ", "_").to_lowercase();

                            if existing_filename != new_filename {
                                // Name has changed, need to rename file
                                old_file_path = Some(entry.path().to_string_lossy().to_string());
                            }
                            break;
                        }
                    }
                }
            }
        }
    }

    // Create new file path for the rotation using the name
    let new_file_path = format!(
        "{}/{}.json",
        CUSTOM_ROTATIONS_JSON_PATH,
        rotation.name.replace(" ", "_").to_lowercase()
    );

    let rotation_to_save = rotation.clone();

    println!("Rotation to save: {:?}", rotation_to_save);

    // Serialize and write the rotation to file
    let json = serde_json::to_string_pretty(&rotation_to_save).map_err(|e| e.to_string())?;
    let mut file = File::create(&new_file_path).map_err(|e| e.to_string())?;
    file.write_all(json.as_bytes()).map_err(|e| e.to_string())?;

    // If this was a rename operation, delete the old files
    if let Some(old_path) = old_file_path {
        if old_path != new_file_path {
            // Remove old JSON file
            if let Err(e) = fs::remove_file(&old_path) {
                eprintln!(
                    "Warning: Failed to remove old rotation file {}: {}",
                    old_path, e
                );
            } else {
                println!("Removed old rotation file: {}", old_path);
            }

            // Also remove old Lua folder if it exists
            if let Some(old_filename) = std::path::Path::new(&old_path).file_stem() {
                if let Some(old_name) = old_filename.to_str() {
                    let old_lua_folder = format!(
                        "{}/{}/{}/{}",
                        CUSTOM_ROTATIONS_PATH,
                        rotation_to_save.game.to_lowercase(),
                        rotation_to_save.class.to_lowercase(),
                        old_name
                    );

                    if std::path::Path::new(&old_lua_folder).exists() {
                        if let Err(e) = fs::remove_dir_all(&old_lua_folder) {
                            eprintln!(
                                "Warning: Failed to remove old Lua folder {}: {}",
                                old_lua_folder, e
                            );
                        } else {
                            println!("Removed old Lua folder: {}", old_lua_folder);
                        }
                    }
                }
            }
        }
    }

    // Generate Lua files for this specific rotation
    if let Err(e) = generate_lua_for_single_rotation(&rotation_to_save) {
        eprintln!("Warning: Failed to generate Lua files for rotation: {}", e);
    }

    // Update the global rotations list
    if let Err(e) = update_global_rotations_list() {
        eprintln!("Warning: Failed to update global rotations list: {}", e);
    }

    // Update keybinds for the saved rotation
    if let Err(e) = update_keybinds_for_custom_rotation(&rotation_to_save) {
        eprintln!("Warning: Failed to update keybinds for rotation: {}", e);
    }

    Ok(())
}

#[tauri::command]
pub async fn delete_custom_rotation(id: String) -> Result<(), String> {
    // Read all files in the custom_rotations/priorities directory
    let entries = fs::read_dir(CUSTOM_ROTATIONS_JSON_PATH).map_err(|e| e.to_string())?;

    for entry in entries {
        if let Ok(entry) = entry {
            if let Some(extension) = entry.path().extension() {
                if extension == "json" {
                    let contents = fs::read_to_string(entry.path()).map_err(|e| e.to_string())?;
                    let mut rotation: CustomRotation =
                        serde_json::from_str(&contents).map_err(|e| e.to_string())?;

                    // Populate sectionId for actions that don't have it (for compatibility)
                    for section in &mut rotation.sections {
                        for action in &mut section.actions {
                            if action.sectionId.is_none() {
                                action.sectionId = Some(section.id.clone());
                            }
                        }
                    }

                    if rotation.id == id {
                        // Delete the file
                        fs::remove_file(entry.path()).map_err(|e| e.to_string())?;

                        // Update the global rotations list
                        if let Err(e) = update_global_rotations_list() {
                            eprintln!("Warning: Failed to update global rotations list: {}", e);
                        }

                        return Ok(());
                    }
                }
            }
        }
    }

    Err("Rotation not found".to_string())
}

#[tauri::command]
pub async fn load_custom_rotations() -> Result<Vec<CustomRotation>, String> {
    let mut rotations = Vec::new();

    // Create the directory if it doesn't exist
    if !std::path::Path::new(CUSTOM_ROTATIONS_JSON_PATH).exists() {
        fs::create_dir_all(CUSTOM_ROTATIONS_JSON_PATH).map_err(|e| e.to_string())?;
        return Ok(rotations); // Return empty vector if directory was just created
    }

    // Read all files in the custom_rotations/priorities directory
    let entries = fs::read_dir(CUSTOM_ROTATIONS_JSON_PATH).map_err(|e| e.to_string())?;

    for entry in entries {
        if let Ok(entry) = entry {
            if let Some(extension) = entry.path().extension() {
                if extension == "json" {
                    let contents = fs::read_to_string(entry.path()).map_err(|e| e.to_string())?;
                    let rotation: CustomRotation =
                        serde_json::from_str(&contents).map_err(|e| e.to_string())?;

                    rotations.push(rotation);
                }
            }
        }
    }

    Ok(rotations)
}

/// Generate Lua files for a single custom rotation
fn generate_lua_for_single_rotation(
    rotation: &CustomRotation,
) -> Result<(), Box<dyn std::error::Error>> {
    log(&format!("Generating Lua for rotation: {}", rotation.name));

    // Generate Lua code from the rotation
    let lua_code = generate_rotation_lua(rotation)?;

    // Create the proper folder structure: custom_rotations/game/class/rotation_name/
    let rotation_folder = format!(
        "{}/{}/{}/{}",
        CUSTOM_ROTATIONS_PATH,
        rotation.game.to_lowercase(),
        rotation.class.to_lowercase(),
        rotation.name.replace(" ", "_").to_lowercase()
    );

    // Create the directory structure
    fs::create_dir_all(&rotation_folder)?;

    // Write the main rotation Lua file as Priorities.lua
    let lua_file_path = format!("{}/Priorities.lua", rotation_folder);
    let mut lua_file = File::create(&lua_file_path)?;
    lua_file.write_all(lua_code.as_bytes())?;

    // Copy RStatus.lua to Libs/RStatus folder (shared across all rotations)
    copy_rstatus_to_libs()?;

    // Create a proper rotation config file (config.toml)
    let config_path = format!("{}/config.toml", rotation_folder);

    // Convert CustomRotation spells to RotationConfigSpell format
    let spells = rotation
        .spells
        .iter()
        .map(|spell| {
            format!(
                r#"[[spells]]
name = "{}"
spell_ids = [{}]"#,
                spell.name,
                spell
                    .spell_ids
                    .iter()
                    .map(|id| id.to_string())
                    .collect::<Vec<_>>()
                    .join(", ")
            )
        })
        .collect::<Vec<_>>()
        .join("\n");

    // Create empty macros section if none exist
    let macros = if rotation.macros.is_some() && !rotation.macros.as_ref().unwrap().is_empty() {
        rotation
            .macros
            .as_ref()
            .unwrap()
            .iter()
            .map(|macro_| {
                format!(
                    r#"[[macros]]
name = "{}"
content = "{}"
spell_ids = [{}]"#,
                    macro_.name,
                    macro_.content.replace("\"", "\\\""),
                    macro_
                        .spell_ids
                        .iter()
                        .map(|id| id.to_string())
                        .collect::<Vec<_>>()
                        .join(", ")
                )
            })
            .collect::<Vec<_>>()
            .join("\n")
    } else {
        String::new()
    };

    // Create the config.toml content
    let config_content = format!(
        r#"name = "{}"
game = "{}"
class = "{}"
spec = "{}"
load_order = ["Priorities.lua"]
version = "{}"
author = "{}"
{}
{}
"#,
        rotation.name,
        rotation.game.to_lowercase(),
        rotation.class,
        rotation.spec,
        rotation.version,
        rotation.author,
        if !macros.is_empty() {
            "\n".to_string() + &macros
        } else {
            String::new()
        },
        if !spells.is_empty() {
            "\n".to_string() + &spells
        } else {
            String::new()
        }
    );

    let mut config_file = File::create(&config_path)?;
    config_file.write_all(config_content.as_bytes())?;

    log(&format!(
        "Generated Lua files for {} in {}",
        rotation.name, rotation_folder
    ));

    Ok(())
}

/// Generate Lua files from all JSON custom rotations and organize them in proper folder structure
pub fn generate_lua_from_custom_rotations() -> Result<(), Box<dyn std::error::Error>> {
    log("Starting Lua generation from custom rotations...");

    // Create the directory if it doesn't exist
    if !std::path::Path::new(CUSTOM_ROTATIONS_JSON_PATH).exists() {
        fs::create_dir_all(CUSTOM_ROTATIONS_JSON_PATH)?;
        log("Created custom_rotations/priorities directory");
        return Ok(()); // Return early if directory was just created (no files to process)
    }

    // Read all JSON files from custom_rotations/priorities directory
    let entries = fs::read_dir(CUSTOM_ROTATIONS_JSON_PATH)?;

    for entry in entries {
        let entry = entry?;
        if let Some(extension) = entry.path().extension() {
            if extension == "json" {
                let contents = fs::read_to_string(entry.path())?;
                let mut rotation: CustomRotation = serde_json::from_str(&contents)?;

                // Populate sectionId for actions that don't have it
                for section in &mut rotation.sections {
                    for action in &mut section.actions {
                        if action.sectionId.is_none() {
                            action.sectionId = Some(section.id.clone());
                        }
                    }
                }

                log(&format!("Generating Lua for rotation: {}", rotation.name));

                // Generate Lua code from the rotation
                let lua_code = generate_rotation_lua(&rotation)?;

                // Create the proper folder structure: custom_rotations/game/class/rotation_name/
                let rotation_folder = format!(
                    "{}/{}/{}/{}",
                    CUSTOM_ROTATIONS_PATH,
                    rotation.game.to_lowercase(),
                    rotation.class.to_lowercase(),
                    rotation.name.replace(" ", "_").to_lowercase()
                );

                // Create the directory structure
                fs::create_dir_all(&rotation_folder)?;

                // Write the main rotation Lua file as Priorities.lua
                let lua_file_path = format!("{}/Priorities.lua", rotation_folder);
                let mut lua_file = File::create(&lua_file_path)?;
                lua_file.write_all(lua_code.as_bytes())?;

                // Copy RStatus.lua to Libs/RStatus folder (shared across all rotations)
                copy_rstatus_to_libs()?;

                // Create a proper rotation config file (config.toml)
                let config_path = format!("{}/config.toml", rotation_folder);

                // Convert CustomRotation spells to RotationConfigSpell format
                let spells = rotation
                    .spells
                    .iter()
                    .map(|spell| {
                        format!(
                            r#"[[spells]]
name = "{}"
spell_ids = [{}]"#,
                            spell.name,
                            spell
                                .spell_ids
                                .iter()
                                .map(|id| id.to_string())
                                .collect::<Vec<_>>()
                                .join(", ")
                        )
                    })
                    .collect::<Vec<_>>()
                    .join("\n");

                // Create empty macros section if none exist
                let macros =
                    if rotation.macros.is_some() && !rotation.macros.as_ref().unwrap().is_empty() {
                        rotation
                            .macros
                            .as_ref()
                            .unwrap()
                            .iter()
                            .map(|macro_| {
                                format!(
                                    r#"[[macros]]
name = "{}"
content = "{}"
spell_ids = [{}]"#,
                                    macro_.name,
                                    macro_.content.replace("\"", "\\\""),
                                    macro_
                                        .spell_ids
                                        .iter()
                                        .map(|id| id.to_string())
                                        .collect::<Vec<_>>()
                                        .join(", ")
                                )
                            })
                            .collect::<Vec<_>>()
                            .join("\n")
                    } else {
                        String::new()
                    };

                // Create the config.toml content
                let config_content = format!(
                    r#"name = "{}"
game = "{}"
class = "{}"
spec = "{}"
load_order = ["Priorities.lua"]
version = "{}"
author = "{}"
{}
{}
"#,
                    rotation.name,
                    rotation.game.to_lowercase(),
                    rotation.class,
                    rotation.spec,
                    rotation.version,
                    rotation.author,
                    if !macros.is_empty() {
                        "\n".to_string() + &macros
                    } else {
                        String::new()
                    },
                    if !spells.is_empty() {
                        "\n".to_string() + &spells
                    } else {
                        String::new()
                    }
                );

                let mut config_file = File::create(&config_path)?;
                config_file.write_all(config_content.as_bytes())?;

                log(&format!(
                    "Generated Lua files for {} in {}",
                    rotation.name, rotation_folder
                ));
            }
        }
    }

    log("Completed Lua generation from custom rotations");
    Ok(())
}

/// Generate Lua code from a CustomRotation struct
fn generate_rotation_lua(rotation: &CustomRotation) -> Result<String, Box<dyn std::error::Error>> {
    // Convert CustomRotation to the format expected by LuaGenerator
    let lua_code = format!(
        r#"

local RotationData = {{
  name = "{}",
  class = "{}",
  spec = "{}",
  spells = {{{}}},
  buffs = {{{}}},
  sections = {{{}}},
}}

local currentSectionId = "{}"
local lastActionTime = 0
local waitUntil = 0

local function ExecuteRotation()
  if waitUntil > GetTime() then
    return
  end

  local currentSection = RotationData.sections[currentSectionId]
  if not currentSection then
    currentSectionId = "{}"
    currentSection = RotationData.sections[currentSectionId]
  end

  if not currentSection then
    return
  end

  for _, action in ipairs(currentSection.actions) do
    local spellData = action.action_type == "CAST" and RotationData.spells[action.spellId]
    if spellData then
        action.spell = spellData
    end
    if RotationHelper:CanExecuteAction(action, RotationData) then
      local executed = ExecuteAction(action)
      if executed then
        lastActionTime = GetTime()
        break
      end
    end
  end
end

function ExecuteAction(action)
  if action.action_type == "CAST" then
    return ExecuteCastAction(action)
  elseif action.action_type == "WAIT" then
    return ExecuteWaitAction(action)
  elseif action.action_type == "START_SECTION" then
    return ExecuteStartSectionAction(action)
  end

  return false
end

function ExecuteCastAction(action)
	if not action.spellId then
		return false
	end

	local spellData = action.spell
	if not spellData then
		return false
	end

	local spellId = nil
	for _, id in pairs(spellData.spell_ids) do
		if RotationHelper.spellIdToCast == id then
			spellId = id
			break
		end
	end

	spellId = spellId or spellData.spell_ids[1]

	if not spellId then
		return false
	end

	local success = OPTI.cast(spellId, false, true)

	OPTI:Print("Casting: " .. spellData.name .. " (" .. spellId .. ") - Success: " .. tostring(success))

	return success
end

function ExecuteWaitAction(action)
  if not action.waitSeconds then return false end

  waitUntil = GetTime() + action.waitSeconds
  OPTI:Print("Waiting for: " .. action.waitSeconds .. " seconds")
  return true
end

function ExecuteStartSectionAction(action)
  if not action.startSectionId then return false end

  local targetSection = RotationData.sections[action.startSectionId]
  if not targetSection then return false end

  currentSectionId = action.startSectionId

  OPTI:Print("Starting section: " .. targetSection.name)
  return true
end

local frame = CreateFrame("Frame", "{}RotationFrame", UIParent)
frame:RegisterEvent("PLAYER_ENTERING_WORLD")
frame:RegisterEvent("PLAYER_REGEN_ENABLED")
frame:RegisterEvent("PLAYER_REGEN_DISABLED")

frame:SetScript("OnEvent", function(self, event, ...)
  if event == "PLAYER_ENTERING_WORLD" then
    OPTI:Print("{} rotation loaded")
  end
end)

frame:SetScript("OnUpdate", function(self, elapsed)
  ExecuteRotation()
end)

OPTI.CustomRotation = {{
    data = RotationData,
    execute = ExecuteRotation,
    getCurrentSection = function() return currentSectionId end,
    setCurrentSection = function(sectionId) currentSectionId = sectionId end
}}"#,
        rotation.name,
        rotation.class.to_uppercase(),
        rotation.spec,
        generate_spells_table(&rotation.spells),
        generate_buffs_table(&rotation.buffs),
        generate_sections_table(&rotation.sections),
        get_first_section_id(&rotation.sections),
        get_first_section_id(&rotation.sections),
        rotation.name.replace(" ", ""),
        rotation.name
    );

    Ok(lua_code)
}

/// Generate the spells table for Lua
fn generate_spells_table(spells: &[crate::types::CustomRotationSpell]) -> String {
    if spells.is_empty() {
        return String::new();
    }

    let spell_entries: Vec<String> = spells
        .iter()
        .map(|spell| {
            let spell_ids = spell
                .spell_ids
                .iter()
                .map(|id| id.to_string())
                .collect::<Vec<_>>()
                .join(", ");

            format!(
                r#"
    ["{}"] = {{
      name = "{}",
      spell_ids = {{{}}}
    }}"#,
                spell.id, spell.name, spell_ids
            )
        })
        .collect();

    spell_entries.join(",\n")
}

/// Generate the buffs table for Lua
fn generate_buffs_table(buffs: &[crate::types::CustomRotationBuff]) -> String {
    if buffs.is_empty() {
        return String::new();
    }

    let buff_entries: Vec<String> = buffs
        .iter()
        .map(|buff| {
            let spell_ids = buff
                .spell_ids
                .iter()
                .map(|id| id.to_string())
                .collect::<Vec<_>>()
                .join(", ");

            format!(
                r#"
    ["{}"] = {{
      name = "{}",
      spell_ids = {{{}}},
      type_ = "{}"
    }}"#,
                buff.id, buff.name, spell_ids, buff.type_
            )
        })
        .collect();

    buff_entries.join(",\n")
}

/// Generate the sections table for Lua
fn generate_sections_table(sections: &[crate::types::CustomRotationSection]) -> String {
    if sections.is_empty() {
        return String::new();
    }

    let section_entries: Vec<String> = sections
        .iter()
        .map(|section| {
            let actions = generate_actions_table(&section.actions);

            format!(
                r#"
    ["{}"] = {{
      name = "{}",
      order = {},
      actions = {{
{}
      }}
    }}"#,
                section.id, section.name, section.order, actions
            )
        })
        .collect();

    section_entries.join(",\n")
}

/// Generate the actions table for Lua
fn generate_actions_table(actions: &[crate::types::CustomRotationAction]) -> String {
    if actions.is_empty() {
        return String::new();
    }

    let action_entries: Vec<String> = actions
        .iter()
        .map(|action| {
            let conditions = generate_conditions_table(&action.conditions);

            let mut action_specific = String::new();
            match action.action_type.as_str() {
                "CAST" => {
                    if let Some(spell_id) = &action.spellId {
                        action_specific = format!(r#"spellId = "{}","#, spell_id);
                    }
                }
                "WAIT" => {
                    if let Some(wait_seconds) = action.waitSeconds {
                        action_specific = format!("waitSeconds = {},", wait_seconds);
                    }
                }
                "START_SECTION" => {
                    if let Some(start_section_id) = &action.startSectionId {
                        action_specific = format!(r#"startSectionId = "{}","#, start_section_id);
                    }
                }
                _ => {}
            }

            let formatted_action = if action_specific.is_empty() {
                format!(
                    r#"        {{
          id = "{}",
          action_type = "{}",
          order = {},
          conditions = {{
{}
          }}
        }}"#,
                    action.id, action.action_type, action.order, conditions
                )
            } else {
                format!(
                    r#"        {{
          id = "{}",
          action_type = "{}",
          order = {},
          {}
          conditions = {{
{}
          }}
        }}"#,
                    action.id, action.action_type, action.order, action_specific, conditions
                )
            };
            formatted_action
        })
        .collect();

    action_entries.join(",\n")
}

/// Generate the conditions table for Lua
fn generate_conditions_table(conditions: &[crate::types::CustomRotationCondition]) -> String {
    if conditions.is_empty() {
        return String::new();
    }

    let condition_entries: Vec<String> = conditions
        .iter()
        .map(|condition| generate_single_condition(condition, 12)) // Start with 12 spaces indentation
        .collect();

    condition_entries.join(",\n")
}

/// Generate a single condition with proper nesting support
fn generate_single_condition(
    condition: &crate::types::CustomRotationCondition,
    indent_level: usize,
) -> String {
    let indent = " ".repeat(indent_level);
    let field_indent = " ".repeat(indent_level + 2);

    let mut fields = vec![
        format!(r#"id = "{}""#, condition.id),
        format!(r#"entityType = "{}""#, condition.entityType),
    ];

    if let Some(property_type) = &condition.propertyType {
        fields.push(format!(r#"propertyType = "{}""#, property_type));
    }

    if let Some(operator) = &condition.operator {
        fields.push(format!(r#"operator = "{}""#, operator));
    }

    if let Some(value) = &condition.value {
        let value_str = match value {
            serde_json::Value::String(s) => format!(r#""{}""#, s),
            serde_json::Value::Number(n) => n.to_string(),
            serde_json::Value::Bool(b) => b.to_string(),
            _ => "nil".to_string(),
        };
        fields.push(format!("value = {}", value_str));
    }

    if let Some(value_type) = &condition.valueType {
        fields.push(format!(r#"valueType = "{}""#, value_type));
    }

    if let Some(logic) = &condition.logic {
        fields.push(format!(r#"logic = "{}""#, logic));
    }

    // Handle buffId field
    if let Some(buff_id) = &condition.buffId {
        fields.push(format!(r#"buffId = "{}""#, buff_id));
    }

    // Handle debuffId field
    if let Some(debuff_id) = &condition.debuffId {
        fields.push(format!(r#"debuffId = "{}""#, debuff_id));
    }

    // Handle checkSpellId field
    if let Some(check_spell_id) = &condition.checkSpellId {
        fields.push(format!(r#"checkSpellId = "{}""#, check_spell_id));
    }

    // Handle nested conditions recursively
    if let Some(nested_conditions) = &condition.conditions {
        if !nested_conditions.is_empty() {
            let nested_entries: Vec<String> = nested_conditions
                .iter()
                .map(|nested_condition| {
                    generate_single_condition(nested_condition, indent_level + 4)
                })
                .collect();

            let nested_conditions_str = format!(
                "conditions = {{\n{}\n{}}}",
                nested_entries.join(",\n"),
                field_indent
            );
            fields.push(nested_conditions_str);
        }
    }

    format!(
        "{}{{\n{}{}\n{}}}",
        indent,
        field_indent,
        fields.join(&format!(",\n{}", field_indent)),
        indent
    )
}

/// Get the first section ID for initial state
fn get_first_section_id(sections: &[crate::types::CustomRotationSection]) -> String {
    sections
        .iter()
        .min_by_key(|s| s.order)
        .map(|s| s.id.clone())
        .unwrap_or_default()
}

/// Copy RStatus.lua to the Libs/RStatus folder
pub fn copy_rstatus_to_libs() -> Result<(), Box<dyn std::error::Error>> {
    use crate::constants::ADDON_FOLDER_TO_COPY_PATH;

    // Create Libs/RStatus directory
    let libs_folder = format!("{}/Libs/RStatus", ADDON_FOLDER_TO_COPY_PATH);
    fs::create_dir_all(&libs_folder)?;

    // Try multiple possible locations for the RStatus.lua file
    let possible_sources = [
        "RStatus.lua",                    // Root directory (development)
        "src-tauri/RStatus.lua",          // src-tauri directory
        "rotation_objects.lua",           // Fallback: Root directory (development)
        "src-tauri/rotation_objects.lua", // Fallback: src-tauri directory
    ];

    let rstatus_dest = format!("{}/RStatus.lua", libs_folder);
    let mut copied = false;

    for src_path in &possible_sources {
        if std::path::Path::new(src_path).exists() {
            if let Ok(_) = fs::copy(src_path, &rstatus_dest) {
                log(&format!("Copied {} to Libs/RStatus/RStatus.lua", src_path));
                copied = true;
                break;
            }
        }
    }

    if !copied {
        log("Warning: Could not find RStatus.lua or rotation_objects.lua in any expected location");
        return Err("RStatus.lua not found".to_string().into());
    }

    Ok(())
}

/// Update keybinds for a custom rotation
fn update_keybinds_for_custom_rotation(
    rotation: &CustomRotation,
) -> Result<(), Box<dyn std::error::Error>> {
    // Convert CustomRotation to RotationConfig for keybind processing
    let rotation_config = custom_rotation_to_rotation_config(rotation);

    // Create a vector with just this rotation for the merge_keybinds function
    let rotations = vec![rotation_config];

    // Use the existing keybinds merge functionality
    keybinds::merge_keybinds(rotations)?;
    Ok(())
}
