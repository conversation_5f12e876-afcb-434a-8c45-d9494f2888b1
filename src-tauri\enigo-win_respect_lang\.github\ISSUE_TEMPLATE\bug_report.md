---
name: Bug report
about: Create a report to help us improve
title: ""
labels: bug, needs investigation
assignees: ""
---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps or a minimal code example to reproduce the behavior.

**Expected behavior**
A clear and concise description of what you expected to happen.

**Environment (please complete the following information):**

- OS: [e.g. Linux, Windows, macOS ..]
- Rust [e.g. rustc --version]
- Library Version [e.g. enigo 0.0.13 or commit hash fa448be ]

**Additional context**
Add any other context about the problem here.
