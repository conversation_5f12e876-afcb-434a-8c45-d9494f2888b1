import { entitySchemas, EntityType, PropertyType } from "../types";

describe("Entity Schemas", () => {
  describe("PLAYER entity", () => {
    it("should have correct allowed properties", () => {
      const playerSchema = entitySchemas.PLAYER;
      const allowedProperties = Object.keys(
        playerSchema.allowedProperties || {}
      );

      expect(allowedProperties).toContain("HEALTH");
      expect(allowedProperties).toContain("RESOURCE");
      expect(allowedProperties).toContain("HAS_BUFF");
      expect(allowedProperties).toContain("IN_COMBAT");
      expect(allowedProperties).toContain("IS_CASTING");
      expect(allowedProperties).toContain("IS_MOVING");
      expect(allowedProperties).toContain("HAS_TALENT");
      expect(allowedProperties).toHaveLength(7);
    });

    it("should have correct value types for HEALTH property", () => {
      const healthProperty = entitySchemas.PLAYER.allowedProperties?.HEALTH;

      expect(healthProperty?.allowedValueTypes).toEqual(["PERCENTAGE"]);
      expect(healthProperty?.allowedOperators).toContain("LESS_THAN");
      expect(healthProperty?.allowedOperators).toContain("GREATER_THAN");
      expect(healthProperty?.allowedOperators).toContain("EQUAL");
    });

    it("should require buffId for HAS_BUFF property", () => {
      const hasBuffProperty = entitySchemas.PLAYER.allowedProperties?.HAS_BUFF;

      expect(hasBuffProperty?.requiredFields).toContain("buffId");
      expect(hasBuffProperty?.allowedValueTypes).toEqual(["BOOLEAN"]);
      expect(hasBuffProperty?.allowedOperators).toEqual(["EQUAL"]);
    });
  });

  describe("TARGET entity", () => {
    it("should have correct allowed properties", () => {
      const targetSchema = entitySchemas.TARGET;
      const allowedProperties = Object.keys(
        targetSchema.allowedProperties || {}
      );

      expect(allowedProperties).toContain("HEALTH");
      expect(allowedProperties).toContain("HAS_DEBUFF");
      expect(allowedProperties).toContain("DEBUFF_REMAINING");
      expect(allowedProperties).toHaveLength(3);
    });

    it("should require debuffId for HAS_DEBUFF property", () => {
      const hasDebuffProperty =
        entitySchemas.TARGET.allowedProperties?.HAS_DEBUFF;

      expect(hasDebuffProperty?.requiredFields).toContain("debuffId");
      expect(hasDebuffProperty?.allowedValueTypes).toEqual(["BOOLEAN"]);
      expect(hasDebuffProperty?.allowedOperators).toEqual(["EQUAL"]);
    });

    it("should require debuffId for DEBUFF_REMAINING property", () => {
      const debuffRemainingProperty =
        entitySchemas.TARGET.allowedProperties?.DEBUFF_REMAINING;

      expect(debuffRemainingProperty?.requiredFields).toContain("debuffId");
      expect(debuffRemainingProperty?.allowedValueTypes).toEqual(["TIME"]);
      expect(debuffRemainingProperty?.allowedOperators).toContain(
        "GREATER_THAN"
      );
      expect(debuffRemainingProperty?.allowedOperators).toContain("LESS_THAN");
    });
  });

  describe("SPELL entity", () => {
    it("should have correct allowed properties", () => {
      const spellSchema = entitySchemas.SPELL;
      const allowedProperties = Object.keys(
        spellSchema.allowedProperties || {}
      );

      expect(allowedProperties).toContain("COOLDOWN");
      expect(allowedProperties).toHaveLength(1);
    });

    it("should require spellId for COOLDOWN property", () => {
      const cooldownProperty = entitySchemas.SPELL.allowedProperties?.COOLDOWN;

      expect(cooldownProperty?.requiredFields).toContain("spellId");
    });

    it("should have correct configuration for COOLDOWN property", () => {
      const cooldownProperty = entitySchemas.SPELL.allowedProperties?.COOLDOWN;

      expect(cooldownProperty?.allowedValueTypes).toEqual(["SECONDS"]);
      expect(cooldownProperty?.allowedOperators).toContain("EQUAL");
      expect(cooldownProperty?.allowedOperators).toContain("LESS_THAN");
      expect(cooldownProperty?.allowedOperators).toContain("GREATER_THAN");
    });
  });

  describe("Schema validation", () => {
    it("should not allow invalid entity types", () => {
      const invalidEntity = "INVALID" as EntityType;
      expect(entitySchemas[invalidEntity]).toBeUndefined();
    });

    it("should not allow invalid property types for entities", () => {
      const playerSchema = entitySchemas.PLAYER;
      const invalidProperty = "INVALID_PROPERTY" as PropertyType;

      expect(playerSchema.allowedProperties?.[invalidProperty]).toBeUndefined();
    });

    it("should ensure all entities have proper structure", () => {
      Object.entries(entitySchemas).forEach(([entityType, schema]) => {
        expect(schema).toHaveProperty("allowedProperties");

        if (schema.allowedProperties) {
          Object.entries(schema.allowedProperties).forEach(
            ([propertyType, propertySchema]) => {
              expect(propertySchema).toHaveProperty("allowedValueTypes");
              expect(propertySchema).toHaveProperty("allowedOperators");
              expect(Array.isArray(propertySchema.allowedValueTypes)).toBe(
                true
              );
              expect(Array.isArray(propertySchema.allowedOperators)).toBe(true);
              expect(propertySchema.allowedValueTypes.length).toBeGreaterThan(
                0
              );
              expect(propertySchema.allowedOperators.length).toBeGreaterThan(0);
            }
          );
        }
      });
    });
  });
});
