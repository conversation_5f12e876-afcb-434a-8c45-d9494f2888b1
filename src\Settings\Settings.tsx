import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>lide<PERSON>,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Box,
  Text,
  Button,
  FormLabel,
  Input,
} from "@chakra-ui/react";
import { ChangeEvent, useEffect, useState } from "react";
import "./Settings.css";
import { DEFAULT_CONFIG } from "../utils";
import { ConfigType } from "../types";
import { invoke } from "@tauri-apps/api/tauri";

export const Settings = () => {
  const [settings, setSettings] = useState<ConfigType>(DEFAULT_CONFIG);

  useEffect(() => {
    invoke<ConfigType>("get_settings").then((data) => {
      setSettings(data);
    });
  }, []);

  const saveSettings = async () => {
    const jsonStr = JSON.stringify(settings);
    const updatedSettings: ConfigType = await invoke("save_app_settings", {
      payload: jsonStr,
    });
    setSettings(updatedSettings);
  };

  const onChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setSettings((oldState) => ({
      ...oldState,
      [e.target.name]: e.target.value,
    }));
  };

  const handleSliderChange = (value: number, name: string) => {
    setSettings((oldState) => ({
      ...oldState,
      [name]: value,
    }));
  };

  return (
    <Flex
      direction="column"
      gap={7}
      justify="center"
      style={{
        marginTop: "auto",
      }}
      align="left"
    >
      <Box>
        <FormLabel>Addon name</FormLabel>
        <Input
          placeholder="OptiStrike"
          value={settings.addon_name}
          name="addon_name"
          onChange={onChange}
        />
      </Box>
      <Box>
        <Heading size="sm">Check Rate Limit</Heading>
        <Text fontSize="sm">
          Limits the amount of times OptiRotations checks when to press a key
          (lower value = better performance)
        </Text>
        <Slider
          aria-label="Check Rate Limit"
          defaultValue={settings.rate_limit}
          value={settings.rate_limit}
          name="rate_limit"
          min={1}
          max={120}
          step={1}
          maxWidth="400px"
          onChange={(value) => handleSliderChange(value, "rate_limit")}
        >
          <SliderTrack>
            <SliderFilledTrack />
          </SliderTrack>
          <SliderThumb />
        </Slider>
        <Text>
          <b>{settings.rate_limit}</b> times per second
        </Text>
      </Box>
      <Box>
        <Heading size="sm">Keybind press frequency</Heading>
        <Text fontSize="sm">
          Maximum amount of times keybind is pressed per second (random
          deviation is added by default)
        </Text>
        <Slider
          aria-label="Check Rate Limit"
          defaultValue={settings.key_limit}
          value={settings.key_limit}
          min={1}
          max={20}
          step={1}
          name="key_limit"
          maxWidth="400px"
          onChange={(value) => handleSliderChange(value, "key_limit")}
        >
          <SliderTrack>
            <SliderFilledTrack />
          </SliderTrack>
          <SliderThumb />
        </Slider>
        <Text>
          <b>{settings.key_limit}</b> times per second
        </Text>
      </Box>
      <Box mb={4}>
        <Button
          className="btn-settings"
          style={{ position: "absolute", bottom: "16px" }}
          _hover={{
            backgroundPosition: "right center",
            textDecoration: "none",
          }}
          onClick={saveSettings}
        >
          Save settings
        </Button>
      </Box>
    </Flex>
  );
};
