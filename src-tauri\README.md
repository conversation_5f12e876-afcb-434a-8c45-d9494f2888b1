# OptiStrike community rotations

This is a common repo for all community rotations.
Direct pushes to main is forbidden, all updates must be done with pull requests and approved by me.

 If your script uses HeroRotation addon, suffix the file name with **_HeroR**

**Checklist when updating rotations:**

- Create a new branch from main
- Make sure your script is in correct game & class folder
- <PERSON><PERSON><PERSON> name must include author name
- Publish branch, make a PR and add me as reviewer

If your script uses HeroRotation and you need to modify the class files, let me know and I'll update it manually.

### Only 1 file per script!