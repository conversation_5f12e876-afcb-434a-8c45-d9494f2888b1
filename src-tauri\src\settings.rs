use crate::constants::SETTINGS_PATH;
use crate::types::{self, RotationConfig};
use crate::ROTATIONS;
use std::fs::File;
use std::io::Write;
use types::Settings;

const DEFAULT_RATE_LIMIT: u32 = 30;
const DEFAULT_KEY_LIMIT: u32 = 10;

pub fn read_settings_from_file() -> Settings {
    let default_settings: Settings = Settings {
        wow_path: String::new(),
        addon_name: "OptiStrike".to_string(),
        activation_key: Some(String::new()),
        selected_rotation: RotationConfig {
            name: String::new(),
            class: String::new(),
            game: String::new(),
            spec: String::new(),
            spells: Vec::new(),
            macros: Vec::new(),
            author: String::new(),
            version: String::new(),
            dependencies: None,
            original_name: Some(String::new()),
            load_order: Vec::new(),
            is_custom: None,
        },
        rotations: ROTATIONS.lock().unwrap().clone(),
        rate_limit: DEFAULT_RATE_LIMIT,
        key_limit: DEFAULT_KEY_LIMIT,
    };

    if !std::path::Path::new(SETTINGS_PATH).exists() {
        write_settings_to_file(&default_settings).unwrap_or_default();
    }

    match File::open(SETTINGS_PATH) {
        Ok(file) => {
            let reader = std::io::BufReader::new(file);
            match serde_json::from_reader(reader) {
                Ok(settings) => settings,
                Err(_) => default_settings,
            }
        }
        Err(_) => default_settings,
    }
}

pub fn write_settings_to_file(settings: &Settings) -> Result<(), Box<dyn std::error::Error>> {
    let json = serde_json::to_string(settings)?;
    let mut file = File::create(SETTINGS_PATH)?;
    file.write_all(json.as_bytes())?;
    Ok(())
}
