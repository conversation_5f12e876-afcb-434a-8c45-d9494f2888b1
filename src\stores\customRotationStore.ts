import { create } from "zustand";
import { CustomRotation, CustomRotationAction, CustomRotationBuff, CustomRotationCondition, CustomRotationSection, CustomRotationSpell, SimpleCondition } from "../types";

type CustomRotationState = {
  selectedRotation: CustomRotation | null;
  setSelectedRotation: (rotation: CustomRotation | null) => void;
  customRotations: CustomRotation[];
  setCustomRotations: (rotations: CustomRotation[]) => void;
  formState: {
    isOpen: boolean;
    setIsOpen: (isOpen: boolean) => void;
    isEditing: boolean;
    setIsEditing: (isEditing: boolean) => void;
    isLoading: boolean;
    setIsLoading: (isLoading: boolean) => void;
    conditionModalOpen: boolean;
    setConditionModalOpen: (conditionModalOpen: boolean) => void;
    selectedAction: CustomRotationAction | null;
    setSelectedAction: (action: CustomRotationAction | null) => void;
    selectedSection: CustomRotationSection | null;
    setSelectedSection: (section: CustomRotationSection | null) => void;
    selectedCondition: SimpleCondition | null;
    setSelectedCondition: (condition: SimpleCondition | null) => void;
    spells: CustomRotationSpell[];
    editSpell: (spell: CustomRotationSpell) => void;
    deleteSpell: (spell: CustomRotationSpell) => void;
    addSpell: (spell: CustomRotationSpell) => void;
    setSpells: (spells: CustomRotationSpell[]) => void;
    buffs: CustomRotationBuff[];
    editBuff: (buff: CustomRotationBuff) => void;
    deleteBuff: (buff: CustomRotationBuff) => void;
    addBuff: (buff: CustomRotationBuff) => void;
    setBuffs: (buffs: CustomRotationBuff[]) => void;
    sections: CustomRotationSection[];
    editSection: (section: CustomRotationSection) => void;
    deleteSection: (section: CustomRotationSection) => void;
    addSection: (section: CustomRotationSection) => void;
    setSections: (sections: CustomRotationSection[]) => void;
    actions: CustomRotationAction[];
    editAction: (action: CustomRotationAction) => void;
    deleteAction: (action: CustomRotationAction) => void;
    addAction: (action: CustomRotationAction) => void;
    setActions: (actions: CustomRotationAction[]) => void;
    conditions: CustomRotationCondition[];
    setConditions: (conditions: CustomRotationCondition[]) => void;
    editCondition: (condition: CustomRotationCondition) => void;
    deleteCondition: (condition: CustomRotationCondition) => void;
    addCondition: (condition: CustomRotationCondition) => void;
    getChildren: (groupId: string) => CustomRotationCondition[];
    getSectionActions: (sectionId: string) => CustomRotationAction[];
  },
};

export const useCustomRotationStore = create<CustomRotationState>((set, get) => ({
  selectedRotation: null,
  setSelectedRotation: (rotation: CustomRotation | null) => set({ selectedRotation: rotation }),
  customRotations: [],
  setCustomRotations: (rotations: CustomRotation[] | []) => set({ customRotations: rotations }),
  formState: {
    isOpen: false,
    isEditing: false,
    isLoading: false,
    setIsOpen: (isOpen) => set((state) => ({ formState: { ...state.formState, isOpen } })),
    setIsEditing: (isEditing) => set((state) => ({ formState: { ...state.formState, isEditing } })),
    setIsLoading: (isLoading) => set((state) => ({ formState: { ...state.formState, isLoading } })),
    conditionModalOpen: false,
    setConditionModalOpen: (conditionModalOpen) => set((state) => ({ formState: { ...state.formState, conditionModalOpen } })),
    selectedAction: null,
    setSelectedAction: (action) => set((state) => ({ formState: { ...state.formState, selectedAction: action } })),
    selectedSection: null,
    setSelectedSection: (section) => set((state) => ({ formState: { ...state.formState, selectedSection: section } })),
    selectedCondition: null,
    setSelectedCondition: (condition) => set((state) => ({ formState: { ...state.formState, selectedCondition: condition } })),
    spells: [],
    setSpells: (spells) => set((state) => ({ formState: { ...state.formState, spells } })),
    editSpell: (spell) => set((state) => {
      const newSpells = state.formState.spells.map(s => s.id === spell.id ? spell : s);
      return { formState: { ...state.formState, spells: newSpells } };
    }),
    deleteSpell: (spell) => set((state) => {
      const newSpells = state.formState.spells.filter(s => s.id !== spell.id);
      return { formState: { ...state.formState, spells: newSpells } };
    }),
    addSpell: (spell) => set((state) => {
      const newSpells = [...state.formState.spells, spell];
      return { formState: { ...state.formState, spells: newSpells } };
    }),
    buffs: [],
    setBuffs: (buffs) => set((state) => ({ formState: { ...state.formState, buffs } })),
    editBuff: (buff) => set((state) => {
      const newBuffs = state.formState.buffs.map(b => b.id === buff.id ? buff : b);
      return { formState: { ...state.formState, buffs: newBuffs } };
    }),
    deleteBuff: (buff) => set((state) => {
      const newBuffs = state.formState.buffs.filter(b => b.id !== buff.id);
      return { formState: { ...state.formState, buffs: newBuffs } };
    }),
    addBuff: (buff) => set((state) => {
      const newBuffs = [...state.formState.buffs, buff];
      return { formState: { ...state.formState, buffs: newBuffs } };
    }),
    sections: [],
    setSections: (sections) => set((state) => ({ formState: { ...state.formState, sections } })),
    editSection: (section) => set((state) => {
      const newSections = state.formState.sections.map(s => s.id === section.id ? section : s);
      return { formState: { ...state.formState, sections: newSections } };
    }),
    deleteSection: (section) => set((state) => {
      const newSections = state.formState.sections.filter(s => s.id !== section.id);
      return { formState: { ...state.formState, sections: newSections } };
    }),
    addSection: (section) => set((state) => {
      const newSections = [...state.formState.sections, section];
      return { formState: { ...state.formState, sections: newSections } };
    }),
    actions: [],
    setActions: (actions) => set((state) => ({ formState: { ...state.formState, actions } })),
    editAction: (action) => set((state) => {
      const newActions = state.formState.actions.map(a => a.id === action.id ? action : a);
      return { formState: { ...state.formState, actions: newActions } };
    }),
    deleteAction: (action) => set((state) => {
      const newActions = state.formState.actions.filter(a => a.id !== action.id);
      return { formState: { ...state.formState, actions: newActions } };
    }),
    addAction: (action) => set((state) => {
      const newActions = [...state.formState.actions, action];
      return { formState: { ...state.formState, actions: newActions } };
    }),
    conditions: [],
    setConditions: (conditions) => set((state) => ({ formState: { ...state.formState, conditions } })),
    editCondition: (condition) => set((state) => {
      const newConditions = state.formState.conditions.map(c => c.id === condition.id ? condition : c);
      return { formState: { ...state.formState, conditions: newConditions } };
    }),
    deleteCondition: (condition) => set((state) => {
      const newConditions = state.formState.conditions.filter(c => c.id !== condition.id);
      return { formState: { ...state.formState, conditions: newConditions } };
    }),
    addCondition: (condition) => set((state) => {
      const newConditions = [...state.formState.conditions, condition];
      return { formState: { ...state.formState, conditions: newConditions } };
    }),
    getChildren: (groupId: string) => {
      const children = get().formState.conditions.filter(condition => condition.groupId === groupId);
      return children;
    },
    getSectionActions: (sectionId: string) => {
      const actions = get().formState.actions.filter(action => action.sectionId === sectionId);
      return actions;
    },
  },
}));
