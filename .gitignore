# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
settings.json
.env
*.code-workspace
script.lua
OptiStrike.toc
src-tauri/addon_files/*
src-tauri/rotations/*
src-tauri/custom_rotations/*
src-tauri/hekili-retail/*
src-tauri/hekili-classic/*
src-tauri/retail-hero/*
src-tauri/classic-hero/*
src-tauri/conroc/*
src-tauri/target/*
*.exe
keybinds.json
settings.json
.no_download