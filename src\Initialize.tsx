import { But<PERSON>, <PERSON>lex, <PERSON><PERSON>, Text, VStack } from "@chakra-ui/react";
import "./App.css";
import { useEffect, useState } from "react";
import { invoke } from "@tauri-apps/api/tauri";
import { shell } from "@tauri-apps/api";
import { ConfigType } from "./types";
import { useSettingsStore } from "./stores/store";
import { Login } from "./Login";
import App, { CURRENT_VERSION } from "./App";

export const activate = async (key: string) => {
  const response = await fetch("https://www.optistrike.pro/api/activate", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ key }),
  });
  await invoke("save_activation_key", { payload: key });

  return response.ok;
};

export const initialize = async (activation_key: string) => {
  const success = await activate(activation_key ?? "");
  if (success) {
    const initResponse = await invoke<string>("initialize");
    return initResponse;
  }

  return "Activation key is invalid";
};

const get_data = async () => {
  const settingsResponse = await invoke<ConfigType>("get_settings");
  return settingsResponse;
};

type NewVersion = {
  version: string;
  download_url: string;
};

export const Initialize = () => {
  const {
    initialized,
    setSettings,
    setInitialized,
    setActivationKey,
    activationKey,
  } = useSettingsStore();

  const [shouldLogin, setShouldLogin] = useState(false);
  const [newVersion, setNewVersion] = useState<NewVersion | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const check_for_new_version = async () => {
      const response = await fetch("https://www.optistrike.pro/api/version");
      const data = await response.json();
      if (data.version !== CURRENT_VERSION) {
        setNewVersion(data);
      } else {
        setNewVersion(null);
      }
    };
    check_for_new_version();
  }, []);

  useEffect(() => {
    (async () => {
      const activation_key = await invoke<string>("get_activation_key");
      if (activation_key) {
        setActivationKey(activation_key);
      } else {
        setShouldLogin(true);
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      if (activationKey) {
        setShouldLogin(false);
        const initResponse = await initialize(activationKey);
        console.log("Initialized: ", initResponse);
        const initSuccess = initResponse === "initialized";
        setInitialized(initSuccess);
        if (initSuccess) {
          const settings = await get_data();
          setSettings(settings);
        } else {
          setError(initResponse);
          setShouldLogin(true);
        }
      } else {
        setShouldLogin(true);
      }
    })();
  }, [activationKey]);

  if (newVersion) {
    return (
      <Flex alignItems="center" justifyContent="center" height="100vh">
        <VStack>
          <Heading className="title" size="lg">
            OptiStrike Rotations 🔥
          </Heading>
          <Text>
            A new version is available! Please download and install the latest
            version!
          </Text>
          <Button
            width="full"
            size="lg"
            className="btn-grad"
            _hover={{
              backgroundPosition: "right center",
              textDecoration: "none",
            }}
            mt={2}
            onClick={() => {
              shell.open(newVersion.download_url);
            }}
          >
            Download
          </Button>
          <Button
            onClick={() => setNewVersion(null)}
            width="full"
            size="lg"
            className="btn-gray"
            mt={2}
          >
            Skip for now
          </Button>
        </VStack>
      </Flex>
    );
  }

  if (initialized) {
    return <App />;
  }

  if (shouldLogin) {
    return <Login error={error} />;
  }

  return (
    <Flex alignItems="center" justifyContent="center" height="100vh">
      <VStack>
        <Heading className="title" size="lg">
          OptiStrike Rotations 🔥
        </Heading>
        <Text>Initializing...</Text>
        {error && (
          <Text color="red.500" fontSize="sm">
            {error}
          </Text>
        )}
      </VStack>
    </Flex>
  );
};
