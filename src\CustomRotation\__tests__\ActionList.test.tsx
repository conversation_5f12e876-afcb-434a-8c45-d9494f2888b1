import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { <PERSON><PERSON><PERSON><PERSON>ider } from "@chakra-ui/react";
import { FormProvider, useForm } from "react-hook-form";
import { ActionList } from "../ActionList";
import { CustomRotation } from "../../types";

// Mock the ConditionGroup component
jest.mock("../ConditionGroup", () => ({
  ConditionGroup: ({ name, index, groupId }: any) => (
    <div data-testid={`condition-group-${groupId}`}>
      Condition Group: {name}[{index}] - {groupId}
    </div>
  ),
}));

const TestWrapper: React.FC<{
  children: React.ReactNode;
  defaultValues?: Partial<CustomRotation>;
}> = ({ children, defaultValues = {} }) => {
  const methods = useForm<CustomRotation>({
    defaultValues: {
      id: "test-rotation",
      name: "Test Rotation",
      class: "Warrior",
      game: "WoW",
      spec: "Arms",
      author: "Test Author",
      version: "1.0.0",
      sections: [
        {
          id: "section-1",
          name: "Test Section",
          order: 1,
          actions: [
            {
              id: "action-1",
              action_type: "CAST",
              order: 1,
              conditions: [],
              spellId: "spell-1",
              sectionId: "section-1",
            },
            {
              id: "action-2",
              action_type: "WAIT",
              order: 2,
              conditions: [
                {
                  id: "group-1",
                  entityType: "GROUP",
                  propertyType: "CONDITION",
                  logic: "AND",
                  conditions: [
                    {
                      id: "condition-1",
                      entityType: "PLAYER",
                      propertyType: "HEALTH",
                      operator: "LESS_THAN",
                      value: 50,
                      valueType: "PERCENTAGE",
                      groupId: "group-1",
                    },
                  ],
                  groupId: "action-2",
                },
              ],
              waitSeconds: 2,
              sectionId: "section-1",
            },
            {
              id: "action-3",
              action_type: "START_SECTION",
              order: 3,
              conditions: [],
              startSectionId: "section-2",
              sectionId: "section-1",
            },
          ],
        },
        {
          id: "section-2",
          name: "Second Section",
          order: 2,
          actions: [],
        },
      ],
      spells: [
        { id: "spell-1", name: "Mortal Strike", spell_ids: [12294] },
        { id: "spell-2", name: "Overpower", spell_ids: [7384] },
      ],
      buffs: [
        {
          id: "buff-1",
          name: "Battle Shout",
          spell_ids: [6673],
          type_: "BUFF",
        },
      ],
      is_custom: true,
      type: "CUSTOM",
      ...defaultValues,
    },
  });

  return (
    <ChakraProvider>
      <FormProvider {...methods}>{children}</FormProvider>
    </ChakraProvider>
  );
};

describe("ActionList", () => {
  const defaultProps = {
    sectionId: "section-1",
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the action list with action cards", () => {
    render(
      <TestWrapper>
        <ActionList {...defaultProps} />
      </TestWrapper>
    );

    // Check that action cards are rendered (there should be multiple)
    expect(screen.getAllByText("Action Type")).toHaveLength(3);
    expect(screen.getAllByText("Value")).toHaveLength(3);
    expect(screen.getAllByText("Conditions")).toHaveLength(3);
  });

  it("returns null when section is not found", () => {
    render(
      <TestWrapper>
        <ActionList sectionId="non-existent-section" />
      </TestWrapper>
    );

    // Should not render action cards when section is not found
    expect(screen.queryByText("Action Type")).not.toBeInTheDocument();
    expect(screen.queryByText("Value")).not.toBeInTheDocument();
    expect(screen.queryByText("Conditions")).not.toBeInTheDocument();
  });

  describe("Action Types", () => {
    it("displays CAST action with spell selection", () => {
      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      expect(screen.getByDisplayValue("Cast")).toBeInTheDocument();
      expect(screen.getByDisplayValue("Mortal Strike")).toBeInTheDocument();
    });

    it("displays WAIT action with time input", () => {
      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      expect(screen.getByDisplayValue("Wait")).toBeInTheDocument();
      expect(screen.getByDisplayValue("2")).toBeInTheDocument();
    });

    it("displays START_SECTION action with section selection", () => {
      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      expect(screen.getByDisplayValue("Start Section")).toBeInTheDocument();
      expect(screen.getByDisplayValue("Second Section")).toBeInTheDocument();
    });

    it("allows changing action type", async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      const actionTypeSelects = screen.getAllByDisplayValue("Cast");
      const firstActionTypeSelect = actionTypeSelects[0];

      await user.selectOptions(firstActionTypeSelect, "WAIT");

      // Check that the select value has changed
      expect(firstActionTypeSelect).toHaveValue("WAIT");
    });
  });

  describe("Action Management", () => {
    it("displays action management buttons with tooltips", async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      const addConditionButtons = screen.getAllByLabelText(
        "Add condition group"
      );
      expect(addConditionButtons).toHaveLength(3); // One for each action

      // Test tooltip
      await user.hover(addConditionButtons[0]);
      expect(screen.getByText("Add condition group")).toBeInTheDocument();
    });

    it("displays move up/down buttons with correct disabled states", () => {
      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      const moveUpButtons = screen.getAllByLabelText("Move action up");
      const moveDownButtons = screen.getAllByLabelText("Move action down");

      // First action's move up button should be disabled
      expect(moveUpButtons[0]).toBeDisabled();
      expect(moveUpButtons[1]).not.toBeDisabled();
      expect(moveUpButtons[2]).not.toBeDisabled();

      // Last action's move down button should be disabled
      expect(moveDownButtons[0]).not.toBeDisabled();
      expect(moveDownButtons[1]).not.toBeDisabled();
      expect(moveDownButtons[2]).toBeDisabled();
    });

    it("allows moving actions up and down", async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      const moveDownButtons = screen.getAllByLabelText("Move action down");
      await user.click(moveDownButtons[0]);

      // The button should still be there after the action
      expect(screen.getAllByLabelText("Move action down")).toHaveLength(3);
    });

    it("allows deleting actions", async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      const deleteButtons = screen.getAllByLabelText("Delete action");
      expect(deleteButtons).toHaveLength(3);

      await user.click(deleteButtons[0]);

      // After deletion, there should be fewer delete buttons
      expect(screen.getAllByLabelText("Delete action")).toHaveLength(2);
    });
  });

  describe("Condition Management", () => {
    it("displays existing condition groups", () => {
      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      expect(screen.getByTestId("condition-group-group-1")).toBeInTheDocument();
    });

    it("displays simple conditions with proper formatting", () => {
      const customDefaultValues = {
        sections: [
          {
            id: "section-1",
            name: "Test Section",
            order: 1,
            actions: [
              {
                id: "action-1",
                action_type: "CAST" as const,
                order: 1,
                conditions: [
                  {
                    id: "simple-condition-1",
                    entityType: "PLAYER" as const,
                    propertyType: "HEALTH" as const,
                    operator: "LESS_THAN" as const,
                    value: 50,
                    valueType: "PERCENTAGE" as const,
                    groupId: "action-1",
                  },
                ],
                spellId: "spell-1",
                sectionId: "section-1",
              },
            ],
          },
        ],
      };

      render(
        <TestWrapper defaultValues={customDefaultValues}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      expect(
        screen.getByText("PLAYER HEALTH LESS_THAN 50%")
      ).toBeInTheDocument();
    });

    it("displays different unit suffixes for different property types", () => {
      const customDefaultValues = {
        sections: [
          {
            id: "section-1",
            name: "Test Section",
            order: 1,
            actions: [
              {
                id: "action-1",
                action_type: "CAST" as const,
                order: 1,
                conditions: [
                  {
                    id: "health-condition",
                    entityType: "PLAYER" as const,
                    propertyType: "HEALTH" as const,
                    operator: "LESS_THAN" as const,
                    value: 50,
                    valueType: "PERCENTAGE" as const,
                    groupId: "action-1",
                  },
                  {
                    id: "resource-condition",
                    entityType: "PLAYER" as const,
                    propertyType: "RESOURCE" as const,
                    operator: "GREATER_THAN" as const,
                    value: 80,
                    valueType: "PERCENTAGE" as const,
                    groupId: "action-1",
                  },
                  {
                    id: "cooldown-condition",
                    entityType: "SPELL" as const,
                    propertyType: "COOLDOWN" as const,
                    operator: "EQUAL" as const,
                    value: 0,
                    valueType: "NUMBER" as const,
                    groupId: "action-1",
                  },
                  {
                    id: "debuff-condition",
                    entityType: "TARGET" as const,
                    propertyType: "DEBUFF_REMAINING" as const,
                    operator: "GREATER_THAN" as const,
                    value: 5,
                    valueType: "TIME" as const,
                    groupId: "action-1",
                  },
                ],
                spellId: "spell-1",
                sectionId: "section-1",
              },
            ],
          },
        ],
      };

      render(
        <TestWrapper defaultValues={customDefaultValues}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      expect(
        screen.getByText("PLAYER HEALTH LESS_THAN 50%")
      ).toBeInTheDocument();
      expect(
        screen.getByText("PLAYER RESOURCE GREATER_THAN 80%")
      ).toBeInTheDocument();
      expect(screen.getByText("SPELL COOLDOWN EQUAL 0s")).toBeInTheDocument();
      expect(
        screen.getByText("TARGET DEBUFF_REMAINING GREATER_THAN 5s")
      ).toBeInTheDocument();
    });

    it("shows Add Condition Group button when no conditions exist", () => {
      const customDefaultValues = {
        sections: [
          {
            id: "section-1",
            name: "Test Section",
            order: 1,
            actions: [
              {
                id: "action-1",
                action_type: "CAST" as const,
                order: 1,
                conditions: [],
                spellId: "spell-1",
                sectionId: "section-1",
              },
            ],
          },
        ],
      };

      render(
        <TestWrapper defaultValues={customDefaultValues}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      expect(screen.getByText("Add Condition Group")).toBeInTheDocument();
    });

    it("allows adding condition groups", async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      const addConditionButtons = screen.getAllByLabelText(
        "Add condition group"
      );
      await user.click(addConditionButtons[0]);

      // The form should handle adding the condition group
      expect(addConditionButtons[0]).toBeInTheDocument();
    });

    it("allows adding condition groups via the button when no conditions exist", async () => {
      const user = userEvent.setup();

      const customDefaultValues = {
        sections: [
          {
            id: "section-1",
            name: "Test Section",
            order: 1,
            actions: [
              {
                id: "action-1",
                action_type: "CAST" as const,
                order: 1,
                conditions: [],
                spellId: "spell-1",
                sectionId: "section-1",
              },
            ],
          },
        ],
      };

      render(
        <TestWrapper defaultValues={customDefaultValues}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      const addConditionButton = screen.getByText("Add Condition Group");
      await user.click(addConditionButton);

      // After clicking, a condition group should be added and the button should disappear
      expect(screen.queryByText("Add Condition Group")).not.toBeInTheDocument();
    });

    it("displays edit and delete buttons for simple conditions", () => {
      const customDefaultValues = {
        sections: [
          {
            id: "section-1",
            name: "Test Section",
            order: 1,
            actions: [
              {
                id: "action-1",
                action_type: "CAST" as const,
                order: 1,
                conditions: [
                  {
                    id: "simple-condition-1",
                    entityType: "PLAYER" as const,
                    propertyType: "HEALTH" as const,
                    operator: "LESS_THAN" as const,
                    value: 50,
                    valueType: "PERCENTAGE" as const,
                    groupId: "action-1",
                  },
                ],
                spellId: "spell-1",
                sectionId: "section-1",
              },
            ],
          },
        ],
      };

      render(
        <TestWrapper defaultValues={customDefaultValues}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      expect(screen.getByLabelText("Edit condition")).toBeInTheDocument();
      expect(screen.getByLabelText("Delete condition")).toBeInTheDocument();
    });
  });

  describe("Form Integration", () => {
    it("allows editing spell selection for CAST actions", async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      const spellSelects = screen.getAllByDisplayValue("Mortal Strike");
      const firstSpellSelect = spellSelects[0];

      await user.selectOptions(firstSpellSelect, "spell-2");

      expect(screen.getByDisplayValue("Overpower")).toBeInTheDocument();
    });

    it("allows editing wait time for WAIT actions", async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      const waitInput = screen.getByDisplayValue("2");

      await user.clear(waitInput);
      await user.type(waitInput, "5");

      expect(waitInput).toHaveValue(5);
    });

    it("allows editing section selection for START_SECTION actions", () => {
      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      const sectionSelects = screen.getAllByDisplayValue("Second Section");
      const firstSectionSelect = sectionSelects[0];

      // Can only select sections other than the current one (Test Section is filtered out)
      // So we can't select "section-1" (Test Section), but we can verify the current value
      expect(firstSectionSelect).toHaveValue("section-2");

      // The current section (Test Section) should not be available as an option
      expect(
        screen.queryByRole("option", { name: "Test Section" })
      ).not.toBeInTheDocument();
    });

    it("displays all available spells in CAST action dropdown", () => {
      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      expect(screen.getByText("Mortal Strike")).toBeInTheDocument();
      expect(screen.getByText("Overpower")).toBeInTheDocument();
    });

    it("displays available sections in START_SECTION action dropdown (excluding current)", () => {
      render(
        <TestWrapper>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      // Should show other sections but not the current section (Test Section)
      expect(screen.getByText("Second Section")).toBeInTheDocument();
      // Current section should not be shown as an option
      expect(
        screen.queryByRole("option", { name: "Test Section" })
      ).not.toBeInTheDocument();
    });

    it("displays different spell options when different spells are provided", () => {
      const customSpells = [
        { id: "spell-3", name: "Heroic Strike", spell_ids: [78] },
        { id: "spell-4", name: "Rend", spell_ids: [772] },
        { id: "spell-5", name: "Thunder Clap", spell_ids: [6343] },
      ];

      const customDefaultValues = {
        spells: customSpells,
        sections: [
          {
            id: "section-1",
            name: "Test Section",
            order: 1,
            actions: [
              {
                id: "action-1",
                action_type: "CAST" as const,
                order: 1,
                conditions: [],
                spellId: "spell-3",
                sectionId: "section-1",
              },
            ],
          },
        ],
      };

      render(
        <TestWrapper defaultValues={customDefaultValues}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      // Should show the custom spells
      expect(screen.getByText("Heroic Strike")).toBeInTheDocument();
      expect(screen.getByText("Rend")).toBeInTheDocument();
      expect(screen.getByText("Thunder Clap")).toBeInTheDocument();

      // Should not show the default spells
      expect(screen.queryByText("Mortal Strike")).not.toBeInTheDocument();
      expect(screen.queryByText("Overpower")).not.toBeInTheDocument();
    });

    it("displays different section options when different sections are provided", () => {
      const customSections = [
        {
          id: "section-1",
          name: "Main Section",
          order: 1,
          actions: [
            {
              id: "action-1",
              action_type: "START_SECTION" as const,
              order: 1,
              conditions: [],
              startSectionId: "section-3",
              sectionId: "section-1",
            },
          ],
        },
        {
          id: "section-3",
          name: "Third Section",
          order: 2,
          actions: [],
        },
        {
          id: "section-4",
          name: "Fourth Section",
          order: 3,
          actions: [],
        },
      ];

      render(
        <TestWrapper defaultValues={{ sections: customSections }}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      // Should show the custom sections (excluding current section "Main Section")
      expect(screen.getByText("Third Section")).toBeInTheDocument();
      expect(screen.getByText("Fourth Section")).toBeInTheDocument();

      // Should not show the current section or default sections
      expect(
        screen.queryByRole("option", { name: "Main Section" })
      ).not.toBeInTheDocument();
      expect(screen.queryByText("Second Section")).not.toBeInTheDocument();
    });

    it("handles empty spells array gracefully", () => {
      const customDefaultValues = {
        spells: [],
        sections: [
          {
            id: "section-1",
            name: "Test Section",
            order: 1,
            actions: [
              {
                id: "action-1",
                action_type: "CAST" as const,
                order: 1,
                conditions: [],
                spellId: "spell-1",
                sectionId: "section-1",
              },
            ],
          },
        ],
      };

      render(
        <TestWrapper defaultValues={customDefaultValues}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      // Should still render the action type dropdown
      expect(screen.getByDisplayValue("Cast")).toBeInTheDocument();

      // Spell dropdown should be present but empty (except for placeholder)
      const spellSelects = screen.getAllByRole("combobox");
      const spellSelect = spellSelects.find((select) =>
        select.getAttribute("name")?.includes("spellId")
      );
      expect(spellSelect).toBeInTheDocument();
    });

    it("handles single section gracefully for START_SECTION actions", () => {
      const customDefaultValues = {
        sections: [
          {
            id: "section-1",
            name: "Only Section",
            order: 1,
            actions: [
              {
                id: "action-1",
                action_type: "START_SECTION" as const,
                order: 1,
                conditions: [],
                startSectionId: "",
                sectionId: "section-1",
              },
            ],
          },
        ],
      };

      render(
        <TestWrapper defaultValues={customDefaultValues}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      // Should still render the action
      expect(screen.getByDisplayValue("Start Section")).toBeInTheDocument();
      // With only one section, the dropdown should be empty (current section filtered out)
      const selects = screen.getAllByRole("combobox");
      const sectionSelect = selects.find((select) =>
        select.getAttribute("name")?.includes("startSectionId")
      );
      expect(sectionSelect).toBeInTheDocument();
      // The dropdown should have no options (empty because current section is filtered out)
      expect(sectionSelect?.children).toHaveLength(0);
    });

    it("filters out current section from START_SECTION dropdown options", () => {
      const customDefaultValues = {
        sections: [
          {
            id: "section-1",
            name: "Current Section",
            order: 1,
            actions: [
              {
                id: "action-1",
                action_type: "START_SECTION" as const,
                order: 1,
                conditions: [],
                startSectionId: "section-2",
                sectionId: "section-1",
              },
            ],
          },
          {
            id: "section-2",
            name: "Target Section",
            order: 2,
            actions: [],
          },
          {
            id: "section-3",
            name: "Another Section",
            order: 3,
            actions: [],
          },
        ],
      };

      render(
        <TestWrapper defaultValues={customDefaultValues}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      // Should show other sections but not the current section
      expect(screen.getByText("Target Section")).toBeInTheDocument();
      expect(screen.getByText("Another Section")).toBeInTheDocument();
      expect(screen.queryByText("Current Section")).not.toBeInTheDocument();
    });

    it("allows selecting any section except current section in START_SECTION actions", async () => {
      const user = userEvent.setup();

      const customDefaultValues = {
        sections: [
          {
            id: "section-1",
            name: "Main Section",
            order: 1,
            actions: [
              {
                id: "action-1",
                action_type: "START_SECTION" as const,
                order: 1,
                conditions: [],
                startSectionId: "section-2",
                sectionId: "section-1",
              },
            ],
          },
          {
            id: "section-2",
            name: "Second Section",
            order: 2,
            actions: [],
          },
          {
            id: "section-3",
            name: "Third Section",
            order: 3,
            actions: [],
          },
        ],
      };

      render(
        <TestWrapper defaultValues={customDefaultValues}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      // Find the section select dropdown
      const sectionSelects = screen.getAllByDisplayValue("Second Section");
      const sectionSelect = sectionSelects[0];

      // Should be able to change to third section
      await user.selectOptions(sectionSelect, "section-3");
      expect(sectionSelect).toHaveValue("section-3");

      // Should not be able to select the current section (Main Section)
      // because it's not in the options
      expect(
        screen.queryByRole("option", { name: "Main Section" })
      ).not.toBeInTheDocument();
    });

    it("shows empty dropdown when only current section exists", () => {
      const customDefaultValues = {
        sections: [
          {
            id: "section-1",
            name: "Only Section",
            order: 1,
            actions: [
              {
                id: "action-1",
                action_type: "START_SECTION" as const,
                order: 1,
                conditions: [],
                startSectionId: "",
                sectionId: "section-1",
              },
            ],
          },
        ],
      };

      render(
        <TestWrapper defaultValues={customDefaultValues}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      // Should render the dropdown but with no options
      const selects = screen.getAllByRole("combobox");
      const sectionSelect = selects.find((select) =>
        select.getAttribute("name")?.includes("startSectionId")
      );
      expect(sectionSelect).toBeInTheDocument();

      // Should not show the current section as an option
      expect(
        screen.queryByRole("option", { name: "Only Section" })
      ).not.toBeInTheDocument();
    });

    it("updates section options when form sections are updated", () => {
      const TestComponent = () => {
        const form = useForm<CustomRotation>({
          defaultValues: {
            sections: [
              {
                id: "section-1",
                name: "Main Section",
                order: 1,
                actions: [
                  {
                    id: "action-1",
                    action_type: "START_SECTION" as const,
                    order: 1,
                    conditions: [],
                    startSectionId: "section-2",
                    sectionId: "section-1",
                  },
                ],
              },
              {
                id: "section-2",
                name: "Second Section",
                order: 2,
                actions: [],
              },
            ],
          },
        });

        const addNewSection = () => {
          const currentSections = form.getValues("sections");
          form.setValue("sections", [
            ...currentSections,
            {
              id: "section-3",
              name: "New Section",
              order: 3,
              actions: [],
            },
          ]);
        };

        return (
          <FormProvider {...form}>
            <div>
              <button onClick={addNewSection}>Add Section</button>
              <ActionList sectionId="section-1" />
            </div>
          </FormProvider>
        );
      };

      render(<TestComponent />);

      // Initially should only show Second Section
      expect(screen.getByText("Second Section")).toBeInTheDocument();
      expect(screen.queryByText("New Section")).not.toBeInTheDocument();

      // Add a new section
      fireEvent.click(screen.getByText("Add Section"));

      // Should now show both Second Section and New Section
      expect(screen.getByText("Second Section")).toBeInTheDocument();
      expect(screen.getByText("New Section")).toBeInTheDocument();
    });
  });

  describe("Error Handling", () => {
    it("handles missing section gracefully", () => {
      render(
        <TestWrapper>
          <ActionList sectionId="missing-section" />
        </TestWrapper>
      );

      // Should not render the table when section is missing
      expect(screen.queryByText("Type")).not.toBeInTheDocument();
      expect(screen.queryByText("Value")).not.toBeInTheDocument();
      expect(screen.queryByText("Actions")).not.toBeInTheDocument();
    });

    it("handles empty actions array", () => {
      const customDefaultValues = {
        sections: [
          {
            id: "section-1",
            name: "Test Section",
            order: 1,
            actions: [],
          },
        ],
      };

      render(
        <TestWrapper defaultValues={customDefaultValues}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      // With empty actions array, there should be no action cards
      expect(screen.queryByText("Action Type")).not.toBeInTheDocument();
      expect(screen.queryByText("Value")).not.toBeInTheDocument();
      expect(screen.queryByText("Conditions")).not.toBeInTheDocument();
    });

    it("handles missing spells array", () => {
      const customDefaultValues = {
        spells: [],
      };

      render(
        <TestWrapper defaultValues={customDefaultValues}>
          <ActionList {...defaultProps} />
        </TestWrapper>
      );

      // Should still render without errors (there are 3 actions)
      expect(screen.getAllByText("Action Type")).toHaveLength(3);
    });
  });
});
