.title {
  background-image: linear-gradient(
    to right,
    #da22ff 0%,
    #9733ee 51%,
    #da22ff 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.btn-grad {
  background-image: linear-gradient(
    to right,
    #da22ff 0%,
    #9733ee 51%,
    #da22ff 100%
  );
  transition: 0.5s;
  background-size: 200% auto;
  color: white;
  border-radius: 10px;
}

.btn-grad:hover {
  background-position: right center;
  color: #fff;
  text-decoration: none;
}

.btn-gray {
  background-image: linear-gradient(
    90deg,
    rgba(26, 26, 32, 1) 0%,
    rgba(16, 16, 20, 1) 50%,
    rgba(33, 42, 43, 1) 100%
  );
  text-align: center;
  transition: 0.5s;
  background-size: 200% auto;
  color: white;
  border-radius: 10px;
}
