import { getValueValidation } from "../../types";

describe("Value Validation", () => {
  describe("NUMBER validation", () => {
    const validation = getValueValidation("NUMBER");

    it("should accept valid numbers", () => {
      expect(validation.validate!(42)).toBe(true);
      expect(validation.validate!(0)).toBe(true);
      expect(validation.validate!(-10)).toBe(true);
      expect(validation.validate!(3.14)).toBe(true);
    });

    it("should reject non-numbers", () => {
      expect(validation.validate!("abc")).toBe("Must be a valid number");
      expect(validation.validate!("")).toBe("Must be a valid number");
      expect(validation.validate!(null)).toBe("Must be a valid number");
    });
  });

  describe("PERCENTAGE validation", () => {
    const validation = getValueValidation("PERCENTAGE");

    it("should accept valid percentages", () => {
      expect(validation.validate!(0)).toBe(true);
      expect(validation.validate!(50)).toBe(true);
      expect(validation.validate!(100)).toBe(true);
      expect(validation.validate!(25.5)).toBe(true);
    });

    it("should reject invalid percentages", () => {
      expect(validation.validate!(-1)).toBe("Percentage cannot be negative");
      expect(validation.validate!(101)).toBe("Percentage cannot exceed 100");
      expect(validation.validate!("abc")).toBe("Must be a valid number");
    });
  });

  describe("SECONDS validation", () => {
    const validation = getValueValidation("SECONDS");

    it("should accept valid seconds", () => {
      expect(validation.validate!(0)).toBe(true);
      expect(validation.validate!(30)).toBe(true);
      expect(validation.validate!(1.5)).toBe(true);
    });

    it("should reject negative seconds", () => {
      expect(validation.validate!(-1)).toBe("Seconds cannot be negative");
      expect(validation.validate!("abc")).toBe("Must be a valid number");
    });
  });

  describe("TIME validation", () => {
    const validation = getValueValidation("TIME");

    it("should accept valid time values", () => {
      expect(validation.validate!(0)).toBe(true);
      expect(validation.validate!(10)).toBe(true);
      expect(validation.validate!(2.5)).toBe(true);
    });

    it("should reject negative time", () => {
      expect(validation.validate!(-1)).toBe("Time cannot be negative");
      expect(validation.validate!("abc")).toBe("Must be a valid number");
    });
  });

  describe("ID validation", () => {
    const validation = getValueValidation("ID");

    it("should accept valid IDs", () => {
      expect(validation.validate!(1)).toBe(true);
      expect(validation.validate!(12345)).toBe(true);
      expect(validation.validate!(0)).toBe(true);
    });

    it("should reject invalid IDs", () => {
      expect(validation.validate!(-1)).toBe("ID cannot be negative");
      expect(validation.validate!(1.5)).toBe("ID must be a whole number");
      expect(validation.validate!("abc")).toBe("ID must be a valid number");
    });
  });

  describe("BOOLEAN validation", () => {
    const validation = getValueValidation("BOOLEAN");

    it("should accept valid boolean values", () => {
      expect(validation.validate!(true)).toBe(true);
      expect(validation.validate!(false)).toBe(true);
      expect(validation.validate!("true")).toBe(true);
      expect(validation.validate!("false")).toBe(true);
    });

    it("should reject invalid boolean values", () => {
      expect(validation.validate!("yes")).toBe("Must be true or false");
      expect(validation.validate!(1)).toBe("Must be true or false");
      expect(validation.validate!("")).toBe("Must be true or false");
    });
  });

  describe("STRING validation", () => {
    const validation = getValueValidation("STRING");

    it("should accept valid strings", () => {
      expect(validation.validate!("hello")).toBe(true);
      expect(validation.validate!("123")).toBe(true);
    });

    it("should have required validation", () => {
      expect(validation.required).toBe("Value is required");
    });
  });
});
