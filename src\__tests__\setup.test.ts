// Basic setup test to verify Je<PERSON> is working correctly

describe('Jest Setup', () => {
  it('should be able to run basic tests', () => {
    expect(1 + 1).toBe(2);
  });

  it('should have access to Jest globals', () => {
    expect(jest).toBeDefined();
    expect(describe).toBeDefined();
    expect(it).toBeDefined();
    expect(expect).toBeDefined();
  });

  it('should have crypto.randomUUID mocked', () => {
    const uuid1 = crypto.randomUUID();
    const uuid2 = crypto.randomUUID();
    
    expect(uuid1).toBeDefined();
    expect(uuid2).toBeDefined();
    expect(uuid1).not.toBe(uuid2);
    expect(uuid1).toMatch(/^test-uuid-/);
  });

  it('should have window.matchMedia mocked', () => {
    expect(window.matchMedia).toBeDefined();
    const mediaQuery = window.matchMedia('(min-width: 768px)');
    expect(mediaQuery.matches).toBe(false);
  });
});
