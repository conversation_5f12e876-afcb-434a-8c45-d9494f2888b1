{"name": "optistrike", "private": true, "version": "1.2.3", "type": "module", "scripts": {"dev": "tauri dev", "dev:download": "del .no_download 2>nul & tauri dev", "dev:front": "vite", "build:front": "tsc && vite build", "build": "tauri build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "tauri": "tauri"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.8.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@tauri-apps/api": "^1.5.1", "framer-motion": "^10.16.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "tauri-plugin-store-api": "https://github.com/tauri-apps/tauri-plugin-store.git", "uuid": "^11.1.0", "yarn": "^1.22.22", "zustand": "^5.0.0"}, "devDependencies": {"@tauri-apps/cli": "^1.5.11", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.15.24", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.0.2", "vite": "^4.4.4"}}