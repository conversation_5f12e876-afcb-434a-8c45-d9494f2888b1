local addonName, OPTI = ...

if not addonName then
	return
end

Player = Player or {}
Target = Target or {}
Spell = Spell or {}
RotationHelper = {
	spellIdToCast = nil,
}

-- Helper function to get spell info safely
local function GetSpellInfoSafe(spellId)
	-- Classic/Cataclysm Classic: Use global GetSpellInfo
	if GetSpellInfo then
		return GetSpellInfo(spellId)
	end
	-- Retail: Use C_Spell.GetSpellInfo (returns structured table)
	if C_Spell and C_Spell.GetSpellInfo then
		local spellInfo = C_Spell.GetSpellInfo(spellId)
		if spellInfo then
			return spellInfo.name,
				nil,
				spellInfo.iconID,
				spellInfo.castTime,
				spellInfo.minRange,
				spellInfo.maxRange,
				spellInfo.spellID
		end
	end
	return nil
end

-- Helper function to get spell cooldown safely
local function GetSpellCooldownSafe(spellId)
	if GetSpellCooldown then
		return GetSpellCooldown(spellId)
	end
	if C_Spell and C_Spell.GetSpellCooldown then
		local cooldownInfo = C_Spell.GetSpellCooldown(spellId)
		if cooldownInfo then
			return cooldownInfo.startTime, cooldownInfo.duration, cooldownInfo.isEnabled
		end
	end
	return 0, 0, 0
end

-- Helper function to check if spell is usable safely
local function IsUsableSpellSafe(spellId)
	if IsUsableSpell then
		return IsUsableSpell(spellId)
	end
	if C_Spell and C_Spell.IsSpellUsable then
		return C_Spell.IsSpellUsable(spellId)
	end
	return false, false
end

-- Helper function to check if spell is known safely
local function IsSpellKnownSafe(spellId)
	if IsSpellKnown then
		return IsSpellKnown(spellId)
	end
	if C_Spell and C_Spell.IsSpellKnown then
		return C_Spell.IsSpellKnown(spellId)
	end
	return false
end

-- Helper function to get spell charges safely
local function GetSpellChargesSafe(spellId)
	if GetSpellCharges then
		return GetSpellCharges(spellId)
	end
	if C_Spell and C_Spell.GetSpellCharges then
		local chargeInfo = C_Spell.GetSpellCharges(spellId)
		if chargeInfo then
			return chargeInfo.currentCharges,
				chargeInfo.maxCharges,
				chargeInfo.cooldownStartTime,
				chargeInfo.cooldownDuration
		end
	end
	return 0, 0, 0, 0
end

-- Helper function to check spell range safely
local function IsSpellInRangeSafe(spellId, unit)
	if IsSpellInRange then
		local inRange = IsSpellInRange(spellId, unit)
		return inRange
	end

	if C_Spell and C_Spell.IsSpellInRange then
		local inRange = C_Spell.IsSpellInRange(spellId, unit)
		return inRange
	end

	return nil
end

-- Helper function to find aura by name safely
local function FindAuraByNameSafe(auraName, unit, filter)
	if AuraUtil and AuraUtil.FindAuraByName then
		return AuraUtil.FindAuraByName(auraName, unit, filter)
	end

	-- Classic fallback using UnitBuff/UnitDebuff
	local isHelpful = filter == "HELPFUL"
	local func = isHelpful and UnitBuff or UnitDebuff

	for i = 1, 40 do
		local name, icon, count, dispelType, duration, expirationTime, source, isStealable, nameplateShowPersonal, spellId =
			func(unit, i)
		if not name then
			break
		end

		if name == auraName then
			return {
				name = name,
				icon = icon,
				applications = count or 1,
				dispelName = dispelType,
				duration = duration,
				expirationTime = expirationTime,
				sourceUnit = source,
				isStealable = isStealable,
				nameplateShowPersonal = nameplateShowPersonal,
				spellId = spellId,
			}
		end
	end

	return nil
end

-- Player Health Functions
function Player:GetHealth()
	return UnitHealth("player")
end

function Player:GetMaxHealth()
	return UnitHealthMax("player")
end

function Player:GetHealthPercent()
	local current = self:GetHealth()
	local max = self:GetMaxHealth()
	if max == 0 then
		return 0
	end
	return (current / max) * 100
end

-- Player Resource Functions (Mana, Energy, Rage, etc.)
function Player:GetResource()
	return UnitPower("player")
end

function Player:GetMaxResource()
	return UnitPowerMax("player")
end

function Player:GetResourcePercent()
	local current = self:GetResource()
	local max = self:GetMaxResource()
	if max == 0 then
		return 0
	end
	return (current / max) * 100
end

function Player:GetResourceType()
	return UnitPowerType("player")
end

-- Player Combat State
function Player:IsInCombat()
	return UnitAffectingCombat("player")
end

function Player:IsCasting()
	local name, text, texture, startTime, endTime, isTradeSkill, castID, notInterruptible = UnitCastingInfo("player")
	return name ~= nil
end

function Player:IsChanneling()
	local name, text, texture, startTime, endTime, isTradeSkill, notInterruptible = UnitChannelInfo("player")
	return name ~= nil
end

-- Player Buff/Debuff Functions
function Player:HasBuff(spellIdOrName)
	if type(spellIdOrName) == "number" then
		local name = GetSpellInfoSafe(spellIdOrName)
		if not name then
			return false
		end
		return FindAuraByNameSafe(name, "player", "HELPFUL") ~= nil
	else
		return FindAuraByNameSafe(spellIdOrName, "player", "HELPFUL") ~= nil
	end
end

function Player:GetBuffDuration(spellIdOrName)
	local auraData
	if type(spellIdOrName) == "number" then
		local name = GetSpellInfoSafe(spellIdOrName)
		if not name then
			return 0
		end
		auraData = FindAuraByNameSafe(name, "player", "HELPFUL")
	else
		auraData = FindAuraByNameSafe(spellIdOrName, "player", "HELPFUL")
	end

	if not auraData then
		return 0
	end
	if not auraData.expirationTime then
		return 0
	end
	return auraData.expirationTime - GetTime()
end

function Player:GetBuffStacks(spellIdOrName)
	local auraData
	if type(spellIdOrName) == "number" then
		local name = GetSpellInfoSafe(spellIdOrName)
		if not name then
			return 0
		end
		auraData = FindAuraByNameSafe(name, "player", "HELPFUL")
	else
		auraData = FindAuraByNameSafe(spellIdOrName, "player", "HELPFUL")
	end

	if not auraData then
		return 0
	end
	return auraData.applications or 1
end

-- Target Health Functions
function Target:GetHealth()
	if not UnitExists("target") then
		return 0
	end
	return UnitHealth("target")
end

function Target:GetMaxHealth()
	if not UnitExists("target") then
		return 0
	end
	return UnitHealthMax("target")
end

function Target:GetHealthPercent()
	if not UnitExists("target") then
		return 0
	end
	local current = self:GetHealth()
	local max = self:GetMaxHealth()
	if max == 0 then
		return 0
	end
	return (current / max) * 100
end

-- Target Existence and Validity
function Target:Exists()
	return UnitExists("target")
end

function Target:IsEnemy()
	if not UnitExists("target") then
		return false
	end
	return UnitCanAttack("player", "target")
end

function Target:IsDead()
	if not UnitExists("target") then
		return true
	end
	return UnitIsDead("target")
end

-- Target Debuff Functions
function Target:HasDebuff(spellIdOrName)
	if not UnitExists("target") then
		return false
	end

	if type(spellIdOrName) == "number" then
		local name = GetSpellInfoSafe(spellIdOrName)
		if not name then
			return false
		end
		return FindAuraByNameSafe(name, "target", "HARMFUL") ~= nil
	else
		return FindAuraByNameSafe(spellIdOrName, "target", "HARMFUL") ~= nil
	end
end

function Target:GetDebuffDuration(spellIdOrName)
	if not UnitExists("target") then
		return 0
	end

	local auraData
	if type(spellIdOrName) == "number" then
		local name = GetSpellInfoSafe(spellIdOrName)
		if not name then
			return 0
		end
		auraData = FindAuraByNameSafe(name, "target", "HARMFUL")
	else
		auraData = FindAuraByNameSafe(spellIdOrName, "target", "HARMFUL")
	end

	if not auraData then
		return 0
	end
	if not auraData.expirationTime then
		return 0
	end
	return auraData.expirationTime - GetTime()
end

function Target:GetDebuffStacks(spellIdOrName)
	if not UnitExists("target") then
		return 0
	end

	local auraData
	if type(spellIdOrName) == "number" then
		local name = GetSpellInfoSafe(spellIdOrName)
		if not name then
			return 0
		end
		auraData = FindAuraByNameSafe(name, "target", "HARMFUL")
	else
		auraData = FindAuraByNameSafe(spellIdOrName, "target", "HARMFUL")
	end

	if not auraData then
		return 0
	end
	return auraData.applications or 1
end

-- Spell Cooldown Functions
function Spell:GetCooldown(spellIdOrName)
	local spellId = spellIdOrName
	if type(spellIdOrName) == "string" then
		spellId = select(7, GetSpellInfoSafe(spellIdOrName))
	end

	if not spellId then
		return 0
	end

	local start, duration = GetSpellCooldownSafe(spellId)
	if not start or not duration then
		return 0
	end

	if duration == 0 then
		return 0
	end

	local remaining = (start + duration) - GetTime()
	return math.max(0, remaining)
end

function Spell:IsReady(spellIdOrName)
	return self:GetCooldown(spellIdOrName) == 0
end

function Spell:IsUsable(spellIdOrName)
	local spellId = spellIdOrName
	if type(spellIdOrName) == "string" then
		spellId = select(7, GetSpellInfoSafe(spellIdOrName))
	end

	if not spellId then
		return false
	end

	local usable, noMana = IsUsableSpellSafe(spellId)
	return usable and not noMana
end

function Spell:IsKnown(spellIdOrName)
	local spellId = spellIdOrName
	if type(spellIdOrName) == "string" then
		spellId = select(7, GetSpellInfoSafe(spellIdOrName))
	end

	if not spellId then
		return false
	end

	return IsSpellKnownSafe(spellId)
end

function Spell:GetCharges(spellIdOrName)
	local spellId = spellIdOrName
	if type(spellIdOrName) == "string" then
		spellId = select(7, GetSpellInfoSafe(spellIdOrName))
	end

	if not spellId then
		return 0
	end

	local charges, maxCharges, start, duration = GetSpellChargesSafe(spellId)
	return charges or 0
end

function Spell:GetMaxCharges(spellIdOrName)
	local spellId = spellIdOrName
	if type(spellIdOrName) == "string" then
		spellId = select(7, GetSpellInfoSafe(spellIdOrName))
	end

	if not spellId then
		return 0
	end

	local charges, maxCharges, start, duration = GetSpellChargesSafe(spellId)
	return maxCharges or 0
end

function Spell:RequiresTarget(spellIdOrName)
	local spellId = spellIdOrName
	if type(spellIdOrName) == "string" then
		spellId = select(7, GetSpellInfoSafe(spellIdOrName))
	end

	if not spellId then
		return false
	end

	local playerRange = IsSpellInRangeSafe(spellId, "player")
	if playerRange == nil then
		return false
	end

	local noTargetRange = IsSpellInRangeSafe(spellId)
	if noTargetRange == nil then
		return false
	end

	local selfRange = IsSpellInRangeSafe(spellId, "player")
	local targetRange = UnitExists("target") and IsSpellInRangeSafe(spellId, "target") or nil

	if selfRange == 1 and targetRange == 0 and UnitExists("target") then
		return false
	end

	return true
end

function Spell:IsInRange(spellIdOrName, unit)
	local spellId = spellIdOrName
	if type(spellIdOrName) == "string" then
		spellId = select(7, GetSpellInfoSafe(spellIdOrName))
	end

	if not spellId then
		return false
	end

	if not self:RequiresTarget(spellId) then
		return true
	end

	unit = unit or "target"
	if not UnitExists(unit) then
		return false
	end

	local inRange = IsSpellInRangeSafe(spellId, unit)

	if inRange == nil then
		return true
	end

	return inRange == 1
end

function Spell:GetGCD()
	local start, duration = GetSpellCooldownSafe(61304)
	if not start or not duration then
		return 0
	end

	if duration == 0 then
		return 0
	end

	local remaining = (start + duration) - GetTime()
	if remaining <= 0.3 then
		return 0
	end
	return math.max(0, remaining)
end

function Spell:IsGCDReady()
	return self:GetGCD() == 0
end

function RotationHelper:EvaluateCondition(condition, rotation_data)
	if not condition then
		return true
	end

	local entityType = condition.entityType
	local propertyType = condition.propertyType
	local operator = condition.operator
	local value = condition.value
	local checkSpell = nil

	if condition.buffId then
		-- Look up buff data from rotation_data.buffs
		if rotation_data and rotation_data.buffs and rotation_data.buffs[condition.buffId] then
			local buffData = rotation_data.buffs[condition.buffId]
			checkSpell = buffData
		end
	elseif condition.checkSpellId then
		-- Look up spell data from rotation_data.spells
		if rotation_data and rotation_data.spells and rotation_data.spells[condition.checkSpellId] then
			local spellData = rotation_data.spells[condition.checkSpellId]
			checkSpell = spellData
		end
	elseif condition.debuffId then
		if rotation_data and rotation_data.buffs and rotation_data.buffs[condition.debuffId] then
			local debuffData = rotation_data.buffs[condition.debuffId]
			checkSpell = debuffData
		end
	end

	local actualValue = self:GetPropertyValue(entityType, propertyType, checkSpell)
	return self:CompareValues(tostring(actualValue), operator, tostring(value))
end

function RotationHelper:GetPropertyValue(entityType, propertyType, checkSpell)
	local spellIds = checkSpell.spell_ids

	if entityType == "PLAYER" then
		if propertyType == "HEALTH" then
			return Player:GetHealthPercent()
		elseif propertyType == "RESOURCE" then
			return Player:GetResourcePercent()
		elseif propertyType == "IN_COMBAT" then
			return Player:IsInCombat()
		elseif propertyType == "IS_CASTING" then
			return Player:IsCasting() or Player:IsChanneling()
		elseif propertyType == "HAS_BUFF" and #spellIds > 0 then
			for _, spellId in ipairs(spellIds) do
				if Player:HasBuff(spellId) then
					return true
				end
			end
			return false
		end
	elseif entityType == "TARGET" then
		if propertyType == "HEALTH" then
			return Target:GetHealthPercent()
		elseif propertyType == "HAS_DEBUFF" and #spellIds > 0 then
			for _, spellId in ipairs(spellIds) do
				if Target:HasDebuff(spellId) then
					return true
				end
			end
			return false
		elseif propertyType == "DEBUFF_REMAINING" and #spellIds > 0 then
			local maxDuration = 0
			for _, spellId in ipairs(spellIds) do
				local duration = Target:GetDebuffDuration(spellId)
				if duration > maxDuration then
					maxDuration = duration
				end
			end
			return maxDuration
		end
	elseif entityType == "SPELL" then
		if propertyType == "COOLDOWN" and #spellIds > 0 then
			local minCooldown = math.huge
			for _, spellId in ipairs(spellIds) do
				local cooldown = Spell:GetCooldown(spellId)
				if cooldown < minCooldown then
					minCooldown = cooldown
				end
			end
			return minCooldown == math.huge and 0 or minCooldown
		end
	end

	return 0
end

function RotationHelper:CompareValues(actualValue, operator, expectedValue)
	if operator == "EQUAL" then
		local result = actualValue == expectedValue
		return result
	elseif operator == "LESS_THAN" then
		local result = actualValue < expectedValue
		return result
	elseif operator == "GREATER_THAN" then
		local result = actualValue > expectedValue
		return result
	elseif operator == "LESS_THAN_OR_EQUAL" then
		local result = actualValue <= expectedValue
		return result
	elseif operator == "GREATER_THAN_OR_EQUAL" then
		local result = actualValue >= expectedValue
		return result
	end

	OPTI:Print("Unknown operator: " .. tostring(operator))
	return false
end

function RotationHelper:EvaluateConditionGroup(conditionGroup, rotation_data)
	if not conditionGroup or not conditionGroup.conditions then
		return true
	end

	local logic = conditionGroup.logic or "AND"
	local results = {}

	for _, condition in ipairs(conditionGroup.conditions) do
		local result
		if condition.entityType == "GROUP" then
			result = self:EvaluateConditionGroup(condition, rotation_data)
		else
			result = self:EvaluateCondition(condition, rotation_data)
		end
		table.insert(results, result)
	end

	if logic == "AND" then
		for _, result in ipairs(results) do
			if not result then
				return false
			end
		end
		return true
	elseif logic == "OR" then
		for _, result in ipairs(results) do
			if result then
				return true
			end
		end
		return false
	end

	return true
end

local function CanExecute(action, rotation_data)
	if not action then
		return false
	end

	if action.conditions then
		for _, condition in ipairs(action.conditions) do
			local result
			if condition.entityType == "GROUP" then
				result = RotationHelper:EvaluateConditionGroup(condition, rotation_data)
			else
				result = RotationHelper:EvaluateCondition(condition, rotation_data)
			end

			if not result then
				return false
			end
		end
	end

	-- Check action-specific requirements
	if action.action_type == "CAST" then
		if not action.spell then
			return false
		end

		local any_spell_castable = false
		RotationHelper.spellIdToCast = nil

		for _, spell_id in ipairs(action.spell.spell_ids) do
			local requires_target = Spell:RequiresTarget(spell_id)
			local spell_ready = Spell:IsReady(spell_id)
			local spell_usable = Spell:IsUsable(spell_id)
			local has_target = requires_target and Target:Exists() and Target:IsEnemy() and not Target:IsDead()

			if requires_target then
				local spell_in_range = Spell:IsInRange(spell_id)
				if spell_ready and spell_usable and spell_in_range and has_target then
					any_spell_castable = true
					RotationHelper.spellIdToCast = spell_id
					break
				end
			elseif spell_ready and spell_usable then
				any_spell_castable = true
				RotationHelper.spellIdToCast = spell_id
			end
		end

		return any_spell_castable
	elseif action.action_type == "WAIT" then
		return true
	elseif action.action_type == "START_SECTION" then
		return true
	end

	return false
end

function RotationHelper:CanExecuteAction(action, rotation_data)
	if not action then
		return false
	end

	local canExecute = CanExecute(action, rotation_data)
	local spellName = (action.spell and action.spell.name)
	if not canExecute then
		if action.spell and action.spell.spell_ids then
			for _, spellId in ipairs(action.spell.spell_ids) do
				OPTI:ResetPixel(spellId)
			end
		else
			OPTI:Print("Action " .. spellName .. " does not have spell IDs")
		end
		return false
	end

	return true
end

-- Utility Functions for Rotation Logic
function RotationHelper:GetTimeToNextGCD()
	return Spell:GetGCD()
end

function RotationHelper:ShouldInterrupt()
	if not Target:Exists() or Target:IsDead() then
		return false
	end

	local name, text, texture, startTime, endTime, isTradeSkill, castID, notInterruptible = UnitCastingInfo("target")
	if name and not notInterruptible then
		return true
	end

	local channelName, channelText, channelTexture, channelStartTime, channelEndTime, channelIsTradeSkill, channelNotInterruptible =
		UnitChannelInfo("target")
	if channelName and not channelNotInterruptible then
		return true
	end

	return false
end

function RotationHelper:GetDistanceToTarget()
	if not Target:Exists() then
		return 999
	end

	-- Use CheckInteractDistance for approximate distance
	if CheckInteractDistance("target", 1) then
		return 5
	end -- 5 yards
	if CheckInteractDistance("target", 2) then
		return 11
	end -- 11 yards
	if CheckInteractDistance("target", 3) then
		return 28
	end -- 28 yards
	if CheckInteractDistance("target", 4) then
		return 40
	end -- 40 yards

	return 999 -- Out of range
end

-- Debug and Logging Functions
function RotationHelper:LogCondition(condition, result)
	local message = string.format(
		"Condition: %s.%s %s %s = %s",
		condition.entityType,
		condition.propertyType,
		condition.operator,
		tostring(condition.value),
		tostring(result)
	)
	OPTI:Print(message)
end

function RotationHelper:LogAction(action, canExecute)
	local message = string.format(
		"Action: %s (%s) = %s",
		action.action_type,
		action.spellId or action.waitSeconds or action.startSectionId or "unknown",
		tostring(canExecute)
	)
	OPTI:Print(message)
end
