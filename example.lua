local name, OPTI = ...
local frame = <PERSON>reate<PERSON><PERSON>e("Frame")

-- DO NOT REMOVE
OPTI.spells = {
    { spell = "SPELL SomeSpell", name = "SomeSpell" },
    { spell = "ITEM Managem", name = "Managem" },
	-- Add more spells here
}
OPTI.mapKeybinds(OPTI.spells)
-- DO NOT REMOVE ^

local function PriorityCheck()
	if ShouldCastPyro() then
		OPTI.cast("Pyroblast", false, true)
	end

	if ShouldCastLB() then
		OPTI.cast("Living Bomb", false, true)
	end

    if ShouldCastFireball() then
		OPTI.cast("Fireball", false, false)
	end
end

local function OnUpdate(self, event)
	OPTI.resetPixels() -- Resets all current instructions
	PriorityCheck()
end

frame:SetScript("OnEvent", function(self, event)
	self[event](self)
end)
frame:SetScript("OnUpdate", OnUpdate)
