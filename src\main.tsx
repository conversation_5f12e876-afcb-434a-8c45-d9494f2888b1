import React from "react";
import ReactDOM from "react-dom/client";
import "./styles.css";
import { ChakraProvider, extendTheme, ColorModeScript } from "@chakra-ui/react";
import createCache from "@emotion/cache";
import { CacheProvider } from "@emotion/react";
import { Initialize } from "./Initialize";

const theme = extendTheme({
  initialColorMode: "dark",
  useSystemColorMode: false,
});

const emotionCache = createCache({
  key: "emotion-css-cache",
  prepend: true, // ensures styles are prepended to the <head>, instead of appended
});

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <React.StrictMode>
    <CacheProvider value={emotionCache}>
      <ColorModeScript initialColorMode="dark" />
      <ChakraProvider theme={theme}>
        <Initialize />
      </ChakraProvider>
    </CacheProvider>
  </React.StrictMode>
);
