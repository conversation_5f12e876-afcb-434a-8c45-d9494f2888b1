export type RotationConfigSpell = {
  name: string;
  spell_ids: number[];
};

export type RotationConfigMacro = {
  content: string;
  spell_ids: number[];
  name: string;
};

export type RotationConfig = {
  name: string;
  game: string;
  class: string;
  spec: string;
  author: string;
  version: string;
  spells: RotationConfigSpell[];
  macros: RotationConfigMacro[];
  dependencies?: string[];
  original_name: string;
};

export type ConfigType = {
  wow_path: string;
  addon_name: string;
  activation_key?: string;
  selected_rotation: RotationConfig;
  rotations: RotationConfig[];
  rate_limit: number;
  key_limit: number;
};

export type ConfigInput = Pick<ConfigType, "wow_path" | "selected_rotation">;

export type SettingsType = {
  rate_limit: number;
  addon_name: string;
};

export type Keybind = {
  key: string;
  key_code: number;
  pixel: number;
  spell: string;
  name: string;
  id: string;
};

export type FormKeybind = {
  name: string;
  key: string;
  key_code: number;
  override: boolean;
  modifier?: string;
  display_key: string;
};

export type SavedKeybind = {
  name: string;
  key: string;
  key_code: number;
  override: boolean;
  modifier?: string;
};

export type SavedRotationKeybinds = {
  rotation_id: string;
  keybinds: SavedKeybind[];
};

export interface CustomRotationSpell {
  id: string;
  name: string;
  spell_ids: number[];
}

export interface CustomRotationBuff {
  id: string;
  name: string;
  spell_ids: number[];
  type_: "BUFF" | "DEBUFF";
}

export type EntityType = "PLAYER" | "TARGET" | "SPELL";
export type PropertyType =
  | "HEALTH"
  | "RESOURCE"
  | "IS_CASTING"
  | "COOLDOWN"
  | "HAS_BUFF"
  | "HAS_DEBUFF"
  | "DEBUFF_REMAINING"
  | "IN_COMBAT"
  | "IS_MOVING"
  | "HAS_TALENT";
export type ValueType =
  | "NUMBER"
  | "PERCENTAGE"
  | "BOOLEAN"
  | "TIME"
  | "STRING"
  | "SECONDS"
  | "ID";
export type ComparisonOperator =
  | "LESS_THAN"
  | "GREATER_THAN"
  | "EQUAL"
  | "LESS_THAN_OR_EQUAL"
  | "GREATER_THAN_OR_EQUAL";

export type PropertySchema = {
  allowedProperties: {
    [property in PropertyType]?: {
      allowedValueTypes: ValueType[];
      allowedOperators: ComparisonOperator[];
      requiredFields?: string[]; // e.g. spellId, buffName, debuffId
    };
  };
  requiredFields?: string[]; // e.g. spellId for SPELL
};

export const entitySchemas: Record<EntityType, PropertySchema> = {
  PLAYER: {
    allowedProperties: {
      IN_COMBAT: {
        allowedValueTypes: ["BOOLEAN"],
        allowedOperators: ["EQUAL"],
      },
      IS_CASTING: {
        allowedValueTypes: ["BOOLEAN"],
        allowedOperators: ["EQUAL"],
      },
      HEALTH: {
        allowedValueTypes: ["PERCENTAGE"],
        allowedOperators: [
          "LESS_THAN",
          "GREATER_THAN",
          "EQUAL",
          "LESS_THAN_OR_EQUAL",
          "GREATER_THAN_OR_EQUAL",
        ],
      },
      IS_MOVING: {
        allowedValueTypes: ["BOOLEAN"],
        allowedOperators: ["EQUAL"],
      },
      HAS_TALENT: {
        allowedValueTypes: ["BOOLEAN"],
        allowedOperators: ["EQUAL"],
        requiredFields: ["talentId"],
      },
      RESOURCE: {
        allowedValueTypes: ["PERCENTAGE"],
        allowedOperators: [
          "LESS_THAN",
          "GREATER_THAN",
          "EQUAL",
          "LESS_THAN_OR_EQUAL",
          "GREATER_THAN_OR_EQUAL",
        ],
      },
      HAS_BUFF: {
        allowedValueTypes: ["BOOLEAN"],
        allowedOperators: ["EQUAL"],
        requiredFields: ["buffId"],
      },
    },
  },
  TARGET: {
    allowedProperties: {
      HEALTH: {
        allowedValueTypes: ["PERCENTAGE"],
        allowedOperators: [
          "LESS_THAN",
          "GREATER_THAN",
          "EQUAL",
          "LESS_THAN_OR_EQUAL",
          "GREATER_THAN_OR_EQUAL",
        ],
      },
      HAS_DEBUFF: {
        allowedValueTypes: ["BOOLEAN"],
        allowedOperators: ["EQUAL"],
        requiredFields: ["debuffId"],
      },
      DEBUFF_REMAINING: {
        allowedValueTypes: ["TIME"],
        allowedOperators: [
          "GREATER_THAN",
          "LESS_THAN",
          "EQUAL",
          "LESS_THAN_OR_EQUAL",
          "GREATER_THAN_OR_EQUAL",
        ],
        requiredFields: ["debuffId"],
      },
    },
  },
  SPELL: {
    allowedProperties: {
      COOLDOWN: {
        requiredFields: ["spellId"],
        allowedValueTypes: ["SECONDS"],
        allowedOperators: [
          "EQUAL",
          "LESS_THAN",
          "GREATER_THAN",
          "LESS_THAN_OR_EQUAL",
          "GREATER_THAN_OR_EQUAL",
        ],
      },
    },
  },
};

export type LogicType = "AND" | "OR";

export interface BaseCondition {
  id: string;
}

export interface SimpleCondition extends BaseCondition {
  entityType: EntityType;
  propertyType: PropertyType;
  operator: ComparisonOperator;
  value: string | number | boolean;
  valueType: ValueType;
  checkSpellId?: string;
  buffId?: string;
  debuffId?: string;
  groupId?: string;
}

export interface ConditionGroup extends BaseCondition {
  entityType: "GROUP";
  propertyType: "CONDITION";
  logic: LogicType;
  conditions: (SimpleCondition | ConditionGroup)[];
  groupId?: string;
}

export type CustomRotationCondition = SimpleCondition | ConditionGroup;

// Validation functions for different value types
export const getValueValidation = (valueType: ValueType) => {
  switch (valueType) {
    case "NUMBER":
      return {
        required: "Value is required",
        validate: (value: any) => {
          if (value === "" || value === null || value === undefined)
            return "Must be a valid number";
          const num = Number(value);
          if (isNaN(num)) return "Must be a valid number";
          return true;
        },
      };
    case "PERCENTAGE":
      return {
        required: "Value is required",
        validate: (value: any) => {
          if (value === "" || value === null || value === undefined)
            return "Must be a valid number";
          const num = Number(value);
          if (isNaN(num)) return "Must be a valid number";
          if (num < 0) return "Percentage cannot be negative";
          if (num > 100) return "Percentage cannot exceed 100";
          return true;
        },
      };
    case "SECONDS":
      return {
        required: "Value is required",
        validate: (value: any) => {
          if (value === "" || value === null || value === undefined)
            return "Must be a valid number";
          const num = Number(value);
          if (isNaN(num)) return "Must be a valid number";
          if (num < 0) return "Seconds cannot be negative";
          return true;
        },
      };
    case "TIME":
      return {
        required: "Value is required",
        validate: (value: any) => {
          if (value === "" || value === null || value === undefined)
            return "Must be a valid number";
          const num = Number(value);
          if (isNaN(num)) return "Must be a valid number";
          if (num < 0) return "Time cannot be negative";
          return true;
        },
      };
    case "ID":
      return {
        required: "Value is required",
        validate: (value: any) => {
          if (value === "" || value === null || value === undefined)
            return "ID must be a valid number";
          const num = Number(value);
          if (isNaN(num)) return "ID must be a valid number";
          if (!Number.isInteger(num)) return "ID must be a whole number";
          if (num < 0) return "ID cannot be negative";
          return true;
        },
      };
    case "BOOLEAN":
      return {
        required: "Value is required",
        validate: (value: any) => {
          if (typeof value === "boolean") return true;
          if (value === "true" || value === "false") return true;
          return "Must be true or false";
        },
      };
    case "STRING":
      return {
        required: "Value is required",
        minLength: { value: 1, message: "Value cannot be empty" },
        validate: (value: any) => {
          if (typeof value === "string" && value.length > 0) return true;
          return "Value cannot be empty";
        },
      };
    default:
      return { required: "Value is required" };
  }
};

export type ActionType = "CAST" | "WAIT" | "START_SECTION";

export interface CustomRotationAction {
  id: string;
  action_type: ActionType;
  order: number;
  conditions: CustomRotationCondition[];
  spellId?: string | null; // Only for CAST type
  waitSeconds?: number | null; // Only for WAIT type
  startSectionId?: string | null; // Only for START_SECTION type
  sectionId: string;
}

export interface CustomRotationSection {
  id: string;
  name: string;
  order: number;
  actions: CustomRotationAction[];
}

export interface CustomRotation {
  id: string;
  name: string;
  class: string;
  game: string;
  spec: string;
  author: string;
  version: string;
  sections: CustomRotationSection[];
  spells: CustomRotationSpell[];
  buffs: CustomRotationBuff[];
  is_custom: boolean;
  type: "CUSTOM";
}
