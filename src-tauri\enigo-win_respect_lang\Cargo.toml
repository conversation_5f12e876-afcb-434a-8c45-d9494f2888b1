[package]
name = "enigo"
version = "0.2.0"
authors = [
    "pentamassiv <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
]
edition = "2021"
rust-version = "1.75"
description = "Cross-platform (Linux, Windows, macOS & BSD) library to simulate keyboard and mouse events"
documentation = "https://docs.rs/enigo/"
homepage = "https://github.com/enigo-rs/enigo"
repository = "https://github.com/enigo-rs/enigo"
readme = "README.md"
keywords = ["simulate", "input", "mouse", "keyboard", "automation"]
categories = [
    "development-tools::testing",
    "api-bindings",
    "hardware-support",
    "os",
    "simulation",
]
license = "MIT"
exclude = [".github", "examples", ".gitignore", "rustfmt.toml"]

[package.metadata.docs.rs]
all-features = true

[features]
default = ["xdo"]
libei = ["dep:reis", "dep:ashpd", "dep:pollster", "dep:once_cell"]
serde = ["dep:serde"]
wayland = [
    "dep:wayland-client",
    "dep:wayland-protocols-misc",
    "dep:wayland-protocols-wlr",
    "dep:wayland-protocols-plasma",
    "dep:tempfile",
]
xdo = []
x11rb = ["dep:x11rb"]

[dependencies]
log = "0.4"
serde = { version = "1", features = ["derive"], optional = true }

[target.'cfg(target_os = "windows")'.dependencies]
windows = { version = "0.56", features = [
    "Win32_Foundation",
    "Win32_UI_TextServices",
    "Win32_UI_WindowsAndMessaging",
    "Win32_UI_Input_KeyboardAndMouse",
] }

[target.'cfg(target_os = "macos")'.dependencies]
core-graphics = { version = "0.23", features = ["highsierra"] }
icrate = { version = "0.1", features = [
    "AppKit_all",
] } # AppKit_NSGraphicsContext
objc2 = { version = "0.5", features = ["relax-void-encoding"] }
foreign-types-shared = "0.3"

[target.'cfg(all(unix, not(target_os = "macos")))'.dependencies]
libc = "0.2"
reis = { version = "0.2.0", optional = true }
ashpd = { version = "0.8.1", optional = true }
pollster = { version = "0.3.0", optional = true }
once_cell = { version = "1.19.0", optional = true }
wayland-protocols-misc = { version = "0.2", features = [
    "client",
], optional = true }
wayland-protocols-wlr = { version = "0.2", features = [
    "client",
], optional = true }
wayland-protocols-plasma = { version = "0.2", features = [
    "client",
], optional = true }
wayland-client = { version = "0.31", optional = true }
x11rb = { version = "0.13", features = [
    "randr",
    "xinput",
    "xtest",
], optional = true }
xkbcommon = "0.7"
xkeysym = "0.2"
tempfile = { version = "3", optional = true }

[dev-dependencies]
env_logger = "0.11"
tungstenite = "0.21"
url = "2"
webbrowser = "0.8"
ron = "0.8"

[[example]]
name = "serde"
path = "examples/serde.rs"
required-features = ["serde"]
