import React, { useState } from "react";
import { invoke } from "@tauri-apps/api/tauri";
import {
  Box,
  Button,
  Text,
  VStack,
  Alert,
  AlertIcon,
  Code,
} from "@chakra-ui/react";

export const TestLuaGeneration: React.FC = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleGenerateLua = async () => {
    setIsGenerating(true);
    setResult(null);
    setError(null);

    try {
      const response = await invoke<string>("generate_custom_rotation_lua");
      setResult(response);
    } catch (err) {
      setError(err as string);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Box p={6} maxW="800px" mx="auto">
      <VStack spacing={4} align="stretch">
        <Text fontSize="2xl" fontWeight="bold">
          🧪 Test Lua Generation
        </Text>

        <Text color="gray.600">
          This will generate Lua files from all JSON custom rotations in the
          custom_rotations folder.
        </Text>

        <Button
          colorScheme="blue"
          onClick={handleGenerateLua}
          isLoading={isGenerating}
          loadingText="Generating Lua files..."
          size="lg"
        >
          Generate Lua Files from Custom Rotations
        </Button>

        {result && (
          <Alert status="success">
            <AlertIcon />
            <Box>
              <Text fontWeight="bold">Success!</Text>
              <Text>{result}</Text>
            </Box>
          </Alert>
        )}

        {error && (
          <Alert status="error">
            <AlertIcon />
            <Box>
              <Text fontWeight="bold">Error:</Text>
              <Code fontSize="sm" whiteSpace="pre-wrap">
                {error}
              </Code>
            </Box>
          </Alert>
        )}

        <Box mt={4} p={4} bg="gray.50" borderRadius="md">
          <Text fontSize="sm" color="gray.600">
            <strong>Expected behavior:</strong>
            <br />
            • Reads JSON files from custom_rotations/ folder
            <br />
            • Generates Lua code for each rotation
            <br />
            • Creates folder structure:
            custom_rotations/game/class/rotation_name/
            <br />
            • Copies rotation_objects.lua for backwards compatibility
            <br />• Creates load_order.toml file
          </Text>
        </Box>
      </VStack>
    </Box>
  );
};
