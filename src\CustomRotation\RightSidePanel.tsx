import { Box, VStack, useColorModeValue } from "@chakra-ui/react";
import { SpellsTable } from "./SpellsTable";
import { BuffsTable } from "./BuffsTable";
import { RotationTreeView } from "./RotationTreeView";

export const RightSidePanel = () => {
  const bgColor = useColorModeValue("gray.50", "gray.900");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  return (
    <Box
      bg={bgColor}
      borderLeft="1px"
      borderColor={borderColor}
      height="100%"
      overflowY="auto"
    >
      <VStack spacing={6} p={4} align="stretch">
        {/* Spells Section */}
        <SpellsTable size="sm" compact={true} />

        {/* Buffs Section */}
        <BuffsTable size="sm" compact={true} />

        {/* Tree View */}
        <Box flex={1}>
          <RotationTreeView />
        </Box>
      </VStack>
    </Box>
  );
};
