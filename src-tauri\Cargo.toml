[package]
name = "opti"
version = "1.2.3"
description = "bekisz0"
authors = ["roach"]
license = ""
repository = ""
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.4", features = [] }

[dependencies]
tauri = { version = "1.4", features = [
    "dialog-open",
    "fs-all",
    "protocol-asset",
    "shell-open",
] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
winapi = { version = "0.3.9", features = ["winuser"] }
win-screenshot = "4.0.5"
image = "0.24.7"
directories = "5.0.1"
crossterm = "0.27.0"
tera = "1.19.1"
mlua = { version = "0.9.1", features = ["lua54", "vendored"] }
heck = "0.3.3"
strum = "0.26"
strum_macros = "0.26"
enigo = { git = "https://github.com/enigo-rs/enigo", branch = "main" }
rand = "0.8.4"
tauri-plugin-store = { git = "https://github.com/tauri-apps/plugins-workspace", branch = "v1" }
diesel = { version = "2.1.0", features = ["postgres"] }
thiserror = "1.0.58"
reqwest = { version = "0.11", features = ["blocking"] }
tokio = { version = "1", features = ["full"] }
zip = "0.5"
regex = "1.10.4"
log = "0.4"
flexi_logger = "0.23"
once_cell = "1.17"
lazy_static = "1.4.0"
walkdir = "2.3.2"
libloading = "0.7"
keymap = "0.2.0"
toml = "0.8.19"
dotenv = "0.15.0"
dotenvy = "0.15.7"
dotenvy_macro = "0.15.7"
aws-config = "1.1.5"
aws-sdk-s3 = "1.20.0"


[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
