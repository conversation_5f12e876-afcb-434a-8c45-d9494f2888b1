import { create } from "zustand";
import { ConfigType, RotationConfig } from "../types";
import { DEFAULT_CONFIG } from "../utils";

interface SettingsState {
  settings?: ConfigType;
  setSettings: (settings: ConfigType) => void;
  updateRotations: (rotations: RotationConfig[]) => void;
  selected_rotation: RotationConfig;
  setSelectedRotation: (rotation: RotationConfig) => void;
  loggedIn: boolean;
  setLoggedIn: (loggedIn: boolean) => void;
  initialized: boolean;
  setInitialized: (initialized: boolean) => void;
  setIsInitializing: (isInitializing: boolean) => void;
  isLoading: boolean;
  activationKey: string;
  setActivationKey: (activationKey: string) => void;
}

const normalize_rotations = (rotations: RotationConfig[]) => {
  return rotations.map((rotation) => ({
    ...rotation,
    name: rotation.name.toLowerCase(),
    class: rotation.class.toLowerCase(),
    game: rotation.game.toLowerCase(),
  }));
};

export const useSettingsStore = create<SettingsState>((set) => ({
  settings: undefined,
  setSettings: (settings: ConfigType) =>
    set((old) => ({
      settings: {
        ...settings,
        rotations:
          settings.rotations.length === 0
            ? old.settings?.rotations || []
            : normalize_rotations(settings.rotations),
      },
    })),
  updateRotations: (rotations: RotationConfig[]) =>
    set((state) => ({
      settings: state.settings
        ? {
            ...state.settings,
            rotations: normalize_rotations(rotations),
          }
        : undefined,
    })),
  selected_rotation: DEFAULT_CONFIG.selected_rotation,
  setSelectedRotation: (selected_rotation: RotationConfig) =>
    set({ selected_rotation }),
  loggedIn: false,
  setLoggedIn: (loggedIn: boolean) => set({ loggedIn }),
  initialized: false,
  setInitialized: (initialized: boolean) => set({ initialized }),
  setIsInitializing: (isInitializing: boolean) =>
    set({ isLoading: isInitializing }),
  isLoading: false,
  activationKey: "",
  setActivationKey: (activationKey: string) => set({ activationKey }),
}));
