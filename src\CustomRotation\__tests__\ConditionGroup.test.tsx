import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { <PERSON><PERSON><PERSON>rovider } from "@chakra-ui/react";
import { FormProvider, useForm } from "react-hook-form";
import { ConditionGroup } from "../ConditionGroup";
import {
  CustomRotation,
  ConditionGroup as ConditionGroupType,
} from "../../types";

// Mock form wrapper component
const TestWrapper: React.FC<{
  children: React.ReactNode;
  defaultValues?: Partial<CustomRotation>;
}> = ({ children, defaultValues = {} }) => {
  const methods = useForm<CustomRotation>({
    defaultValues: {
      id: "test-rotation",
      name: "Test Rotation",
      class: "Warrior",
      game: "WoW",
      spec: "Arms",
      author: "Test Author",
      version: "1.0.0",
      sections: [
        {
          id: "section1",
          name: "Test Section",
          order: 1,
          actions: [
            {
              id: "action1",
              action_type: "CAST",
              order: 1,
              conditions: [
                {
                  id: "group1",
                  entityType: "GROUP",
                  propertyType: "CONDITION",
                  logic: "AND",
                  conditions: [
                    {
                      id: "condition1",
                      entityType: "PLAYER",
                      propertyType: "HEALTH",
                      operator: "LESS_THAN",
                      value: 50,
                      valueType: "PERCENTAGE",
                      groupId: "group1",
                    },
                  ],
                  groupId: "action1",
                } as ConditionGroupType,
              ],
              spellId: "spell1",
              sectionId: "section1",
            },
          ],
        },
      ],
      spells: [{ id: "spell1", name: "Mortal Strike", spell_ids: [12294] }],
      buffs: [
        { id: "buff1", name: "Battle Shout", spell_ids: [6673], type_: "BUFF" },
      ],
      is_custom: true,
      type: "CUSTOM",
      ...defaultValues,
    },
  });

  return (
    <ChakraProvider>
      <FormProvider {...methods}>{children}</FormProvider>
    </ChakraProvider>
  );
};

const defaultProps = {
  name: "sections.0.actions.0.conditions",
  groupId: "group1",
  index: 0,
  depth: 0,
};

describe("ConditionGroup", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the condition group with logic selector", () => {
    render(
      <TestWrapper>
        <ConditionGroup {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByDisplayValue("AND")).toBeInTheDocument();
    expect(screen.getByLabelText("Add condition")).toBeInTheDocument();
    expect(
      screen.getByLabelText("Add nested condition group")
    ).toBeInTheDocument();
    expect(screen.getByLabelText("Delete group")).toBeInTheDocument();
  });

  it("displays existing conditions in the group", () => {
    render(
      <TestWrapper>
        <ConditionGroup {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText(/PLAYER HEALTH LESS_THAN 50%/)).toBeInTheDocument();
  });

  it("shows tooltips on hover for action buttons", async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ConditionGroup {...defaultProps} />
      </TestWrapper>
    );

    const addConditionButton = screen.getByLabelText("Add condition");
    await user.hover(addConditionButton);

    expect(screen.getByText("Add condition")).toBeInTheDocument();

    const addGroupButton = screen.getByLabelText("Add nested condition group");
    await user.hover(addGroupButton);

    expect(screen.getByText("Add nested condition group")).toBeInTheDocument();

    const deleteButton = screen.getByLabelText("Delete group");
    await user.hover(deleteButton);

    expect(screen.getByText("Delete group")).toBeInTheDocument();
  });

  it("shows tooltip for logic selector", async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ConditionGroup {...defaultProps} />
      </TestWrapper>
    );

    const logicSelect = screen.getByDisplayValue("AND");
    await user.hover(logicSelect);

    expect(
      screen.getByText(
        "AND: All conditions must be true. OR: At least one condition must be true"
      )
    ).toBeInTheDocument();
  });

  it("can change logic type from AND to OR", async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ConditionGroup {...defaultProps} />
      </TestWrapper>
    );

    const logicSelect = screen.getByDisplayValue("AND");
    await user.selectOptions(logicSelect, "OR");

    expect(screen.getByDisplayValue("OR")).toBeInTheDocument();
  });

  it("opens condition modal when add condition button is clicked", async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ConditionGroup {...defaultProps} />
      </TestWrapper>
    );

    const addConditionButton = screen.getByLabelText("Add condition");
    await user.click(addConditionButton);

    expect(screen.getByText("Add Condition")).toBeInTheDocument();
  });

  it("renders condition edit and delete buttons for individual conditions", () => {
    render(
      <TestWrapper>
        <ConditionGroup {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByLabelText("Edit condition")).toBeInTheDocument();
    expect(screen.getByLabelText("Delete condition")).toBeInTheDocument();
  });

  it("shows tooltips for condition action buttons", async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ConditionGroup {...defaultProps} />
      </TestWrapper>
    );

    const editButton = screen.getByLabelText("Edit condition");
    await user.hover(editButton);

    expect(screen.getByText("Edit condition")).toBeInTheDocument();

    const deleteButton = screen.getByLabelText("Delete condition");
    await user.hover(deleteButton);

    expect(screen.getByText("Delete condition")).toBeInTheDocument();
  });

  it("renders with proper indentation based on depth", () => {
    render(
      <TestWrapper>
        <ConditionGroup {...defaultProps} depth={2} />
      </TestWrapper>
    );

    // Check that the condition group is rendered with the data-testid
    const groupBox = screen.getByTestId("condition-group");
    expect(groupBox).toBeInTheDocument();

    // Verify that the component renders correctly with depth prop
    // The actual margin styling is handled by Chakra UI internally
    expect(groupBox).toBeVisible();
  });
});
