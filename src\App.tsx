import {
  Heading,
  Tab,
  <PERSON>b<PERSON>ndicator,
  <PERSON>b<PERSON><PERSON>,
  <PERSON>bPanel,
  <PERSON>bPanels,
  Tabs,
} from "@chakra-ui/react";
import "./App.css";
import { Home } from "./Home/Home";
import { Settings } from "./Settings/Settings";
import { useEffect, useState } from "react";
import { Keybinds } from "./Keybindings/Keybinds";
import { CustomRotationEditor } from "./CustomRotation/CustomRotationEditor";
import { TestLuaGeneration } from "./TestLuaGeneration";

export const CURRENT_VERSION = "1.2.3";

function App() {
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <Tabs
      variant="unstyled"
      position="relative"
      size="lg"
      height="100vh"
      overflow="hidden"
    >
      <TabList position="fixed" top="0" width="100%" zIndex="1">
        <Tab>Home</Tab>
        <Tab>Keybinds</Tab>
        <Tab>Custom Rotations</Tab>
        <Tab>Settings</Tab>
        <Tab>🧪 Test Lua</Tab>
      </TabList>
      <TabIndicator
        border="5px"
        borderStyle="solid"
        borderTop="none"
        borderLeft="none"
        borderRight="none"
        style={{
          borderImageSource: "linear-gradient(90deg, #7928CA 0%, #FF0080 100%)",
          borderImageSlice: 1,
          borderWidth: "2px",
        }}
      />
      <Heading
        className="title"
        style={{ right: 0, position: "absolute", top: 0 }}
        mt={4}
        mr={4}
        size="md"
      >
        {windowWidth < 550 ? "Opti" : "OptiStrike Rotations 🔥"}
        <span style={{ fontSize: "12px", fontWeight: "normal" }}>
          v{CURRENT_VERSION}
        </span>
      </Heading>

      <TabPanels
        height="calc(100vh - 50px)" // Adjust the height based on your TabList height
        overflowY="auto"
        mt="50px" // Match the height of the TabList
      >
        <TabPanel height="full">
          <Home />
        </TabPanel>
        <TabPanel>
          <Keybinds />
        </TabPanel>
        <TabPanel>
          <CustomRotationEditor />
        </TabPanel>
        <TabPanel>
          <Settings />
        </TabPanel>
        <TabPanel>
          <TestLuaGeneration />
        </TabPanel>
      </TabPanels>
    </Tabs>
  );
}

export default App;
