// Test script to verify Lua generation works
use std::fs;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Add the src-tauri directory to the path so we can use the modules
    let current_dir = std::env::current_dir()?;
    println!("Current directory: {:?}", current_dir);
    
    // Test if the custom_rotations directory exists
    if fs::metadata("custom_rotations").is_ok() {
        println!("✅ custom_rotations directory exists");
        
        // List files in the directory
        let entries = fs::read_dir("custom_rotations")?;
        for entry in entries {
            let entry = entry?;
            println!("Found file: {:?}", entry.file_name());
        }
    } else {
        println!("❌ custom_rotations directory does not exist");
        return Ok(());
    }
    
    // Test if rotation_objects.lua exists
    if fs::metadata("src-tauri/rotation_objects.lua").is_ok() {
        println!("✅ rotation_objects.lua exists");
    } else {
        println!("❌ rotation_objects.lua does not exist");
    }
    
    println!("Test completed successfully!");
    Ok(())
}
