import {
  <PERSON>,
  <PERSON><PERSON>,
  Flex,
  <PERSON>ing,
  VStack,
  HStack,
  useToast,
  useBreakpointValue,
} from "@chakra-ui/react";
import { useEffect } from "react";
import { SectionList } from "./SectionList";
import { BasicInfoSection } from "./BasicInfoSection";
import { RightSidePanel } from "./RightSidePanel";
import { SpellsTable } from "./SpellsTable";
import { BuffsTable } from "./BuffsTable";
import { useCustomRotationStore } from "../stores/customRotationStore";
import { FormProvider, useForm } from "react-hook-form";
import { invoke } from "@tauri-apps/api/tauri";
import { useSettingsStore } from "../stores/store";
import { RotationConfig, CustomRotation } from "../types";

const defaultValues: CustomRotation = {
  id: "",
  name: "",
  class: "",
  game: "",
  spec: "",
  author: "",
  version: "1.0.0",
  sections: [],
  spells: [],
  buffs: [],
  is_custom: true,
  type: "CUSTOM",
};

export const CustomRotationEditor = () => {
  const toast = useToast();
  const { updateRotations } = useSettingsStore();
  const {
    customRotations,
    setCustomRotations,
    selectedRotation,
    setSelectedRotation,
    formState: { isOpen, setIsOpen, setIsLoading, setIsEditing },
  } = useCustomRotationStore();
  const form = useForm<CustomRotation>({
    defaultValues: selectedRotation || defaultValues,
  });
  const { handleSubmit, reset } = form;

  const loadRotations = async () => {
    try {
      setIsLoading(true);
      const loadedRotations = await invoke<CustomRotation[]>(
        "load_custom_rotations"
      );
      setCustomRotations(loadedRotations);
      setIsLoading(false);
      return loadedRotations;
    } catch (error) {
      toast({
        title: "Error loading rotations",
        description: String(error),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      setIsLoading(false);
      return [];
    }
  };

  useEffect(() => {
    const fetchRotations = async () => {
      await loadRotations();
    };

    fetchRotations();
  }, []);

  const clearForm = () => {
    setSelectedRotation(null);
    reset(defaultValues);
  };

  const onSubmit = async (data: CustomRotation) => {
    try {
      setIsLoading(true);
      await invoke("save_custom_rotation", {
        rotation: {
          ...data,
          type: "CUSTOM" as const,
        },
      });

      // Reload custom rotations for the custom rotation editor
      await loadRotations();

      // Update the main rotations list for Config.tsx
      try {
        const updatedRotations = await invoke<RotationConfig[]>(
          "get_current_rotations"
        );
        updateRotations(updatedRotations);
      } catch (error) {
        console.warn("Failed to update main rotations list:", error);
      }

      toast({
        title: "Rotation saved",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
      setIsOpen(false);
      clearForm();
    } catch (error) {
      toast({
        title: "Error saving rotation",
        description: String(error),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      setIsLoading(false);
    }
  };

  const handleFormSubmit = async (data: any) => {
    await onSubmit(data);
  };

  const createNewRotation = () => {
    const newRotation: CustomRotation = {
      ...defaultValues,
      id: crypto.randomUUID(),
    };
    setSelectedRotation(newRotation);
    reset(newRotation);
    setIsOpen(true);
    setIsEditing(false);
  };

  const handleRotationSelect = (rotation: CustomRotation) => {
    setSelectedRotation(rotation);
    reset(rotation);
    setIsOpen(true);
    setIsEditing(true);
  };

  const deleteRotation = async () => {
    setIsLoading(true);

    if (!selectedRotation) {
      return;
    }

    try {
      await invoke("delete_custom_rotation", { id: selectedRotation?.id });

      // Reload custom rotations for the custom rotation editor
      await loadRotations();

      // Update the main rotations list for Config.tsx
      try {
        const updatedRotations = await invoke<RotationConfig[]>(
          "get_current_rotations"
        );
        updateRotations(updatedRotations);
      } catch (error) {
        console.warn("Failed to update main rotations list:", error);
      }

      setSelectedRotation(null);
      setIsOpen(false);
      setIsEditing(false);
      setIsLoading(false);
      toast({
        title: "Rotation deleted",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      setIsLoading(false);
      toast({
        title: "Error deleting rotation",
        description: String(error),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const showTreeView = useBreakpointValue({ base: false, lg: true });

  return (
    <FormProvider {...form}>
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <Flex direction="column" gap={7} height="full" width="full">
          <Flex align="center" gap={6}>
            <Button onClick={() => createNewRotation()}>New Rotation</Button>
          </Flex>
          <Box>
            <Heading size="sm" mb={2}>
              Saved Rotations
            </Heading>
            <HStack spacing={2} wrap="wrap">
              {customRotations.map((rotation) => (
                <Button
                  key={rotation.id}
                  variant={
                    selectedRotation?.id === rotation.id ? "solid" : "ghost"
                  }
                  onClick={() => handleRotationSelect(rotation)}
                >
                  {rotation.name}
                </Button>
              ))}
            </HStack>
          </Box>

          {isOpen && (
            <Box flex={1} position="relative" pb="60px">
              <Flex direction={{ base: "column", lg: "row" }} height="100%">
                {/* Main Content - 60% on large screens, full width on small */}
                <Box
                  flex={{ base: "1", lg: "0 0 60%" }}
                  pr={{ base: 0, md: 4 }}
                  overflowY="auto"
                >
                  <VStack spacing={4} align="stretch">
                    <BasicInfoSection />

                    {/* Show spells and buffs on mobile/small screens */}
                    <Box display={{ base: "block", lg: "none" }}>
                      <Box mb={6}>
                        <SpellsTable />
                      </Box>
                      <BuffsTable />
                    </Box>

                    <Box>
                      <Heading size="sm" mb={4}>
                        Sections
                      </Heading>
                      <SectionList />
                    </Box>
                  </VStack>
                </Box>

                {/* Right Side Panel - 40% on large screens, hidden on small */}
                {showTreeView && (
                  <Box flex="0 0 40%" height="100%">
                    <RightSidePanel />
                  </Box>
                )}
              </Flex>

              <Box
                position="fixed"
                bottom={0}
                left={0}
                right={0}
                p={4}
                bg="gray.700"
                zIndex={1}
              >
                <Flex justify="flex-end" gap={4}>
                  <Button
                    colorScheme="red"
                    onClick={() => deleteRotation()}
                    isDisabled={!selectedRotation}
                    float="left"
                    mr={4}
                  >
                    Delete
                  </Button>
                  <Button onClick={() => setIsOpen(false)}>Cancel</Button>
                  <Button type="submit" colorScheme="blue" isDisabled={false}>
                    Save Rotation
                  </Button>
                </Flex>
              </Box>
            </Box>
          )}
        </Flex>
      </form>
    </FormProvider>
  );
};
