extern crate libloading;
use enigo::{
    Direction::{Click, Press, Release},
    Enigo, Key, Keyboard, Settings,
};
use libloading::{Library, Symbol};
use std::{ffi::OsStr, thread, time::Duration};
use winapi::um::winuser::{GetKeyboardLayout, MapVirtualKeyExW, MAPVK_VK_TO_CHAR};
use winapi::{
    shared::minwindef::HKL,
    um::winuser::{GetAsyncKeyState, VK_CONTROL, VK_MENU, VK_SHIFT},
};

use crate::log_error;
use crate::types::Pixel;

fn load_dll() -> Result<Library, libloading::Error> {
    let lib_path = OsStr::new("libs/driver_simulator.dll");
    unsafe { Library::new(lib_path) }
}

fn init(lib: &Library) -> Result<&Library, libloading::Error> {
    let func: Symbol<unsafe extern "C" fn(i32, i32, i32) -> i32> =
        unsafe { lib.get(b"IbSendInit\0") }?;
    unsafe {
        let result = func(2, 0, 0);
        if result != 0 {
            Err(libloading::Error::DlOpenUnknown)
        } else {
            Ok(lib)
        }
    }
}

fn input_hook(lib: &Library) -> Symbol<'_, unsafe extern "C" fn(i32) -> i32> {
    unsafe {
        lib.get(b"IbSendInputHook\0")
            .map_err(|_| {
                "Driver input error. Please reinstall Logitech driver and restart OptiStrike"
                    .to_string()
            })
            .unwrap()
    }
}

pub fn load_and_init() -> Result<Library, String> {
    let lib = load_dll().map_err(|_| {
        "Failed to initialize Logitech driver. Please install LogitechDriver.exe".to_string()
    })?;
    init(&lib).map_err(|_| {
        "Failed to initialize Logitech driver. Please install LogitechDriver.exe".to_string()
    })?;
    Ok(lib)
}

fn get_current_keyboard_layout() -> HKL {
    unsafe { GetKeyboardLayout(0) }
}

fn keycode_to_char(keycode: u32) -> Option<char> {
    let layout = get_current_keyboard_layout();
    unsafe {
        let result = MapVirtualKeyExW(keycode, MAPVK_VK_TO_CHAR, layout);
        if result == 0 || result > 255 {
            None
        } else {
            Some(std::char::from_u32_unchecked(result))
        }
    }
}

fn is_modifier_pressed(modifier: &str) -> bool {
    let vk = match modifier.to_lowercase().as_str() {
        "shift" => VK_SHIFT,
        "ctrl" => VK_CONTROL,
        "alt" => VK_MENU,
        _ => return false,
    };

    unsafe { (GetAsyncKeyState(vk) as i16) < 0 }
}

pub fn press_key(pixel_to_cast: Pixel, lib: &Library) {
    let mut enigo = Enigo::new(&Settings::default()).unwrap();
    let send_input_hook = input_hook(&lib);
    let key_code = pixel_to_cast.key_code;
    let unicode = pixel_to_cast.key.unwrap_or("".to_string());
    let modifier = pixel_to_cast.modifier.unwrap_or("".to_string());

    if key_code == 0 || unicode.is_empty() {
        eprintln!("Error: No key bound for {}", key_code);
        log_error(&format!("Error: No key bound for {}", key_code));
        return;
    }

    if !modifier.is_empty() {
        let modifier_key_code = match modifier.to_lowercase().as_str() {
            "shift" => 16,
            "ctrl" => 17,
            "alt" => 18,
            _ => 0,
        };
        enigo.key(Key::Other(modifier_key_code), Press);
        // Add small delay after pressing modifier
        thread::sleep(Duration::from_millis(20));

        if !is_modifier_pressed(&modifier) {
            log_error(&format!("Failed to press modifier key: {}", modifier));
            return;
        }
    }

    unsafe { send_input_hook(1) };
    let char = keycode_to_char(key_code as u32).unwrap_or(' ');

    if char == ' ' && !unicode.starts_with("F") {
        eprintln!("Error: Cant press key with key_code {}", key_code);
        log_error(&format!("Error: Cant press key with key_code {}", key_code));
    }

    if key_code < 0 || key_code > 255 {
        eprintln!("Error: Invalid key code {}", key_code);
    } else {
        // Send the raw scancode
        println!("Pressing: {}", key_code);
        let pressed = enigo.key(Key::Other(key_code as u32), Click);
        if pressed.is_err() {
            let error = pressed.err().unwrap();
            log_error(&format!(
                "Error: Failed to press key {}: {:?}",
                key_code, error
            ));
            eprintln!("Error: Failed to press key");
        }
    }
    unsafe { send_input_hook(0) };

    if !modifier.is_empty() {
        // Add small delay before releasing modifier
        thread::sleep(Duration::from_millis(20));
        let modifier_key_code = match modifier.to_lowercase().as_str() {
            "shift" => 16,
            "control" => 17,
            "alt" => 18,
            _ => 0,
        };
        enigo.key(Key::Other(modifier_key_code), Release);
    }
}

pub fn get_available_keys() -> Result<Vec<(String, u32, String)>, String> {
    let defaults: Vec<(&str, u32, Option<&str>)> = vec![
        ("NUMPAD0", 96, None),
        ("NUMPAD1", 97, None),
        ("NUMPAD2", 98, None),
        ("NUMPAD3", 99, None),
        ("NUMPAD4", 100, None),
        ("NUMPAD5", 101, None),
        ("NUMPAD6", 102, None),
        ("NUMPAD7", 103, None),
        ("NUMPAD8", 104, None),
        ("NUMPAD9", 105, None),
        ("NUMPADMULTIPLY", 106, None),
        ("NUMPADPLUS", 107, None),
        ("NUMPADMINUS", 109, None),
        ("NUMPADDIVIDE", 111, None),
        ("F5", 116, None),
        ("F6", 117, None),
        ("F7", 118, None),
        ("F8", 119, None),
        ("F9", 120, None),
        ("F10", 121, None),
        ("F11", 122, None),
        ("0", 48, None),
        ("9", 57, None),
        ("8", 56, None),
        ("7", 55, None),
        ("6", 54, None),
        ("0", 48, Some("Shift")),
        ("9", 57, Some("Shift")),
        ("8", 56, Some("Shift")),
        ("7", 55, Some("Shift")),
        ("6", 54, Some("Shift")),
        ("0", 48, Some("Ctrl")),
        ("9", 57, Some("Ctrl")),
        ("8", 56, Some("Ctrl")),
        ("7", 55, Some("Ctrl")),
        ("6", 54, Some("Ctrl")),
        ("N", 78, Some("Ctrl")),
        ("O", 79, Some("Ctrl")),
        ("K", 75, Some("Ctrl")),
        ("Y", 89, Some("Ctrl")),
        ("N", 78, Some("Shift")),
        ("O", 79, Some("Shift")),
        ("K", 75, Some("Shift")),
        ("Y", 89, Some("Shift")),
    ];

    let lib = load_and_init()?;
    let send_input_hook = input_hook(&lib);
    unsafe { send_input_hook(1) };
    // iterate through localized keys and get char from keycode using keycode_to_char

    unsafe { send_input_hook(0) };

    let all_keys: Vec<(String, u32, String)> = defaults
        .into_iter()
        .map(|(k, v, m)| (k.to_string(), v, m.unwrap_or("").to_string()))
        .collect();
    Ok(all_keys)
}
