use log::info;
use reqwest::header;
use std::{
    fs::{self, File},
    io::{self, BufRead, BufReader, Read, Write},
    path::{Path, PathBuf},
    time::Duration,
};
use toml;
extern crate dotenvy;
use dotenvy_macro::dotenv;

use crate::{
    constants::{ADDON_FOLDER_TO_COPY_PATH, CORE_FILE_NAME, INTERFACES, ROTATIONS_PATH},
    fs_helpers::copy_dir_all,
    keybinds::get_rotation_keybinds,
    types::{RotationConfigMacro, SavedKeybind, SelectedRotation, Settings},
};
use crate::{
    constants::{CUSTOM_ROTATIONS_PATH, DRY_CORE_FILE_NAME},
    fs_helpers::copy_addon_folder_to_wow,
    types::{Pixel, RotationConfig},
};
use zip::ZipArchive;

pub fn check_pixel(pixels: &Vec<Pixel>, name: &str) -> bool {
    if let Some(pixel) = pixels.iter().find(|pixel| pixel.name == name) {
        return pixel.g == 255;
    } else {
        return false;
    };
}

pub fn pixel_to_cast(pixels: &Vec<Pixel>) -> Option<&Pixel> {
    pixels
        .iter()
        .find(|pixel| pixel.should_press && pixel.key_code != 0)
}

pub fn download(file_name: &str, destination: &str) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::blocking::Client::builder()
        .timeout(Duration::from_secs(180))
        .build()?;
    let url = format!(
        "https://pub-2ce9301b75da4657844015afd29bae28.r2.dev/opti-rotations/{}",
        file_name
    );
    // Cloudflare R2 Credentials
    /*     let access_key = dotenv!("TAURI_R2_ACCESS_KEY");
    let secret_key = dotenv!("TAURI_R2_SECRET_KEY");
    let bucket_name = "opti-rotations";

    // Cloudflare R2 endpoint
    let endpoint = dotenv!("TAURI_R2_URL");

    // Set up AWS SDK config for Cloudflare R2
    let credentials = Credentials::new(access_key, secret_key, None, None, "loaded-from-custom");
    let config = Builder::new()
        .region(aws_sdk_s3::config::Region::new("eu-west-1"))
        .credentials_provider(credentials)
        .endpoint_url(endpoint)
        .behavior_version(BehaviorVersion::latest())
        .build();

    let client = aws_sdk_s3::Client::from_conf(config);

    // Download file
    let resp = client
        .get_object()
        .bucket(bucket_name)
        .key(file_name)
        .send()
        .await?; */

    let response = match client.get(url).send() {
        Ok(res) => {
            if res.status().is_success() {
                res.bytes()?
            } else {
                return Err(format!("Failed to download rotations: HTTP {}", res.status()).into());
            }
        }
        Err(e) => return Err(format!("Failed to download rotations: {}", e).into()),
    };

    let mut file = File::create(destination)?;
    file.write_all(&response)?;

    println!("Download complete: {}", file_name);

    Ok(())
}

pub fn download_rotations() -> Result<(), Box<dyn std::error::Error>> {
    let _downloaded = download("repo.zip", "repo.zip")?;

    let output_folder = "";
    let reader = BufReader::new(File::open("repo.zip")?);

    let mut archive = ZipArchive::new(reader)?;

    for i in 0..archive.len() {
        let mut file = archive.by_index(i)?;
        let mut outpath = file.mangled_name();

        // Strip the prefix "opti-rotations-main/" to ignore the top-level folder
        if let Ok(stripped) = outpath.strip_prefix("opti-rotations-1/") {
            outpath = stripped.to_path_buf();
        }

        if outpath.starts_with(".vscode/") {
            continue;
        }

        let full_output_path = Path::new(output_folder).join(&outpath);

        if file.is_dir() {
            std::fs::create_dir_all(&full_output_path)?;
        } else {
            if let Some(p) = outpath.parent() {
                let parent_path = Path::new(output_folder).join(p);
                std::fs::create_dir_all(parent_path)?;
            }
            let mut outfile = File::create(&full_output_path)?;
            std::io::copy(&mut file, &mut outfile)?;
        }
    }
    std::fs::remove_file("repo.zip")?;

    Ok(())
}

pub fn download_community_rotations() -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::blocking::Client::builder()
        .timeout(Duration::from_secs(180))
        .build()?;
    let url = dotenv!("TAURI_R2_URL_COMMUNITY");
    let tkn = dotenv!("TAURI_R2_TOKEN");
    // Download file
    let response = match client.get(url).header(header::AUTHORIZATION, tkn).send() {
        Ok(res) => {
            if res.status().is_success() {
                res.bytes()?
            } else {
                return Err(format!("Failed to download rotations: HTTP {}", res.status()).into());
            }
        }
        Err(e) => return Err(format!("Failed to download rotations: {}", e).into()),
    };

    let mut file = File::create("repo.zip")?;
    file.write_all(&response)?;

    let output_folder = "";
    let reader = BufReader::new(File::open("repo.zip")?);

    let mut archive = ZipArchive::new(reader)?;

    for i in 0..archive.len() {
        let mut file = archive.by_index(i)?;
        let mut outpath = file.mangled_name();

        // Strip the prefix "opti-rotations-main/" to ignore the top-level folder
        if let Ok(stripped) = outpath.strip_prefix("opti-community-rotations-main/") {
            outpath = stripped.to_path_buf();
        }

        if outpath.starts_with(".vscode/") {
            continue;
        }

        let full_output_path = Path::new(output_folder).join(&outpath);

        if file.is_dir() {
            std::fs::create_dir_all(&full_output_path)?;
        } else {
            if let Some(p) = outpath.parent() {
                let parent_path = Path::new(output_folder).join(p);
                std::fs::create_dir_all(parent_path)?;
            }
            let mut outfile = File::create(&full_output_path)?;
            std::io::copy(&mut file, &mut outfile)?;
        }
    }
    std::fs::remove_file("repo.zip")?;

    Ok(())
}

// Reads all rotation config.toml files from the local rotations folder
pub fn get_rotations() -> io::Result<Vec<RotationConfig>> {
    let mut rotations = Vec::new();
    let mut files_to_check = vec![
        PathBuf::from(ROTATIONS_PATH),
        PathBuf::from(CUSTOM_ROTATIONS_PATH),
    ];

    while let Some(path) = files_to_check.pop() {
        if path.is_dir() {
            for entry in fs::read_dir(path)? {
                let entry = entry?;
                files_to_check.push(entry.path());
            }
        } else {
            println!("Checking file: {}", path.display());
            if let Some(ext) = path.extension() {
                if ext != "toml" {
                    continue;
                }

                // Check if the rotation is from the custom folder
                let is_custom = path.starts_with(CUSTOM_ROTATIONS_PATH);
                // Parse the toml file
                let mut file = fs::File::open(&path)?;
                let mut contents = String::new();
                file.read_to_string(&mut contents)?;
                let mut rotation: RotationConfig = match toml::from_str(&contents) {
                    Ok(rotation) => rotation,
                    Err(e) => {
                        eprintln!("Error parsing TOML file {}: {}", path.display(), e);
                        continue;
                    }
                };
                let mut sorted_spells = rotation.spells.clone();
                sorted_spells.sort_by(|a, b| a.name.cmp(&b.name));
                let mut pixel_i = 50;
                for (_, spell) in sorted_spells.iter_mut().enumerate() {
                    spell.pixel = Some(pixel_i);
                    pixel_i += 1;
                }

                let mut sorted_macros = rotation.macros.clone();
                sorted_macros.sort_by(|a, b| a.name.cmp(&b.name));
                for (_, macro_) in sorted_macros.iter_mut().enumerate() {
                    macro_.pixel = Some(pixel_i);
                    pixel_i += 1;
                }
                rotation.macros = sorted_macros;

                rotation.spells = sorted_spells;
                rotation.original_name = Some(rotation.name.clone());
                rotation.name = rotation.name.replace(" ", "_").to_lowercase();
                rotation.is_custom = Some(is_custom);

                rotations.push(rotation);
            }
        }
    }
    println!("Found {} rotations", rotations.len());
    Ok(rotations)
}

fn generate_macros_lua(macros: Vec<RotationConfigMacro>) -> String {
    let mut lua = String::new();
    lua.push_str("local macros = {\n");
    for m in macros {
        let macro_lua = format!(
            "\t{{ name = \"{}\", icon = \"INV_MISC_QUESTIONMARK\", body = \"{}\" }},",
            m.name,
            m.content.replace("\n", "\\n")
        );
        lua.push_str(&macro_lua);
        lua.push_str("\n");
    }
    lua.push_str("}\n");
    return lua;
}

fn generate_spells_lua(rotation: RotationConfig, keybinds: Vec<SavedKeybind>) -> String {
    let mut lua = String::new();
    lua.push_str("local keybinds = {\n");
    lua.push_str("\t{ key = \"\", pixel = 1, spell = \"Started\", name = \"Started\" },\n");
    lua.push_str("\t{ key = \"\", pixel = 2, spell = \"Pause\", name = \"Pause\" },\n");
    lua.push_str("\t{ key = \"\", pixel = 3, spell = \"InCombat\", name = \"InCombat\" },\n");

    for s in &rotation.spells {
        let saved_keydbind = keybinds
            .iter()
            .find(|k| k.name == s.name)
            .unwrap_or(&SavedKeybind {
                key: "".to_string(),
                name: s.name.clone(),
                key_code: 0,
                modifier: None,
            })
            .clone();
        let key = saved_keydbind.key.replace("\\", "\\\\").clone();

        if key.is_empty() {
            println!("No keybind for {}", s.name);
            continue;
        }

        let spell_ids = format!(
            "{{ {} }}",
            s.spell_ids
                .iter()
                .map(|id| id.to_string())
                .collect::<Vec<String>>()
                .join(", ")
        );
        let pixel = s.pixel.unwrap_or(0);
        let stopcast = s.stopcasting.unwrap_or(false);

        let key_bind = if let Some(modifier) = &saved_keydbind.modifier {
            println!("Using modifier {}, for {}", modifier, s.name);
            if !modifier.is_empty() {
                format!("{}-{}", modifier, key)
            } else {
                key.to_string()
            }
        } else {
            key.to_string()
        };

        let spell_lua = format!(
            "\t{{ key = \"{}\", pixel = {}, name = \"{}\", ids = {}, stopcasting = {} }},\n",
            key_bind.to_uppercase(),
            pixel,
            s.name,
            spell_ids,
            stopcast
        );
        lua.push_str(&spell_lua);
    }

    for m in &rotation.macros {
        let saved_keydbind = keybinds
            .iter()
            .find(|k| k.name == m.name)
            .unwrap_or(&SavedKeybind {
                key: "".to_string(),
                name: m.name.clone(),
                key_code: 0,
                modifier: None,
            })
            .clone();
        let key = saved_keydbind.key.replace("\\", "\\\\").clone();

        if key.is_empty() {
            println!("No keybind for {}", m.name);
            continue;
        }

        let spell_ids = format!(
            "{{ {} }}",
            m.spell_ids
                .iter()
                .map(|id| id.to_string())
                .collect::<Vec<String>>()
                .join(", ")
        );
        let pixel = m.pixel.unwrap_or(0);
        let stopcast = m.stopcasting.unwrap_or(false);
        let key_bind = if let Some(modifier) = &saved_keydbind.modifier {
            println!("Using modifier {}, for {}", modifier, m.name);
            if !modifier.is_empty() {
                format!("{}-{}", modifier, key)
            } else {
                key.to_string()
            }
        } else {
            key.to_string()
        };
        let macro_lua = format!(
            "\t{{ key = \"{}\", pixel = {}, name = \"{}\", ids = {}, is_macro = true, stopcasting = {} }},\n",
            key_bind.to_uppercase(), pixel, m.name, spell_ids, stopcast
        );
        lua.push_str(&macro_lua);
    }

    lua.push_str("}");

    println!(
        "Generated spells Lua with {} total entries",
        lua.matches("{ key =").count()
    );

    return lua;
}

fn generate_keybinds_lua(rotation: RotationConfig) -> Result<String, Box<dyn std::error::Error>> {
    let saved_keybinds = get_rotation_keybinds(SelectedRotation {
        game: rotation.game.clone(),
        class: rotation.class.clone(),
        rotation: rotation.name.clone(),
    });

    let mut lua = String::new();
    let macros_lua = generate_macros_lua(rotation.macros.clone());

    let spells_lua = generate_spells_lua(rotation.clone(), saved_keybinds.keybinds);
    lua.push_str(&macros_lua);
    lua.push_str("\n");
    lua.push_str(&spells_lua);
    lua.push_str("\n");

    Ok(lua)
}

pub fn hydrate_core_lua(rotation: RotationConfig) -> Result<(), Box<dyn std::error::Error>> {
    fs::copy(
        format!("{}/{}", ADDON_FOLDER_TO_COPY_PATH, DRY_CORE_FILE_NAME),
        format!("{}/{}", ADDON_FOLDER_TO_COPY_PATH, CORE_FILE_NAME),
    )?;

    let file = File::open(format!("{}/{}", ADDON_FOLDER_TO_COPY_PATH, CORE_FILE_NAME))?;
    let lines: io::Lines<BufReader<File>> = io::BufReader::new(file).lines();
    let mut output = String::new();

    for line in lines {
        let line = line?;
        if line.contains("--HYDRATE--") {
            let keybinds = generate_keybinds_lua(rotation.clone())?;
            output.push_str(&keybinds);
        } else {
            output.push_str(&line);
            output.push('\n');
        }
    }

    let mut file = File::create(format!("{}/{}", ADDON_FOLDER_TO_COPY_PATH, CORE_FILE_NAME))?;
    file.write_all(output.as_bytes())?;
    Ok(())
}

pub fn generate_toc_file(settings: Settings) -> Result<(), Box<dyn std::error::Error>> {
    let folder = Path::new(ADDON_FOLDER_TO_COPY_PATH);
    let selected_rotation = settings.selected_rotation.clone();
    for entry in fs::read_dir(folder)? {
        let entry = entry?;
        let file_path = entry.path();
        let file_name = file_path.file_name().and_then(|f| f.to_str());

        if let Some(file_name) = file_name {
            if file_name.ends_with(".toc") {
                fs::remove_file(file_path)?;
            }
        }
    }

    let toc_path = format!("{}\\{}.toc", ADDON_FOLDER_TO_COPY_PATH, settings.addon_name);

    let version = INTERFACES
        .iter()
        .find(|i| i.name == selected_rotation.game)
        .unwrap_or(INTERFACES.first().unwrap())
        .version;

    let mut contents = String::new();
    let title = format!(
        "## Interface: {}\n## Title: |cff669DFF{}|r\n## Version: {}\n## Author: {}\n\n",
        version, settings.addon_name, selected_rotation.version, selected_rotation.author
    );
    let mut is_hekili = false;

    let dependencies = selected_rotation
        .dependencies
        .as_ref()
        .map_or(String::new(), |deps| {
            if !deps.is_empty() {
                if deps.contains(&"Hekili".to_string()) {
                    is_hekili = true;
                    return String::new();
                } else {
                    format!("## Dependencies: {}\n\n", deps.join(", "))
                }
            } else {
                String::new()
            }
        });

    // Create bindings.xml with <Binding> elements
    let bindings_xml = "<Bindings>\n<Binding name=\"TOGGLE_PAUSE\" description=\"Pause HeroRotation\" category=\"ADDONS\">OPTI:SetConfigValue(\"Pause\", not OPTI:ConfigEnabled(\"Pause\"))</Binding>\n</Bindings>";
    let bindings_xml_path = format!("{}/bindings.xml", ADDON_FOLDER_TO_COPY_PATH);
    let mut bindings_file: File = fs::File::create(bindings_xml_path)?;
    bindings_file.write_all(bindings_xml.as_bytes())?;

    let rotation_load_order = selected_rotation
        .load_order
        .iter()
        .map(|value| format!("{}\\{}", selected_rotation.name, value))
        .collect::<Vec<String>>()
        .join("\n");

    contents.push_str(&title);
    contents.push_str(&dependencies);

    let ace_xml_files = [
        "Libs\\Ace3\\LibStub\\LibStub.lua",
        "Libs\\Ace3\\CallbackHandler-1.0\\CallbackHandler-1.0.xml",
        "Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.xml",
        "Libs\\Ace3\\AceEvent-3.0\\AceEvent-3.0.xml",
        "Libs\\Ace3\\AceTimer-3.0\\AceTimer-3.0.xml",
        "Libs\\Ace3\\AceGUI-3.0\\AceGUI-3.0.xml",
        "Libs\\Ace3\\AceConfig-3.0\\AceConfig-3.0.xml",
        "Libs\\Ace3\\AceConsole-3.0\\AceConsole-3.0.xml",
        "Libs\\Ace3\\AceDB-3.0\\AceDB-3.0.xml",
        "Libs\\Ace3\\AceSerializer-3.0\\AceSerializer-3.0.xml",
        "Libs\\Ace3\\AceDBOptions-3.0\\AceDBOptions-3.0.xml",
        "Libs\\Ace3\\AceTab-3.0\\AceTab-3.0.xml",
    ];

    for lib_path in ace_xml_files.iter() {
        contents.push_str(lib_path);
        contents.push_str("\n");
    }

    contents.push_str(format!("{}\n", CORE_FILE_NAME).as_str());
    let require_files = format!("{}\n", rotation_load_order);
    contents.push_str("Libs\\RStatus\\RStatus.lua\n");
    contents.push_str(&require_files);

    // Create a new .toc file with the provided content
    let mut file: File = fs::File::create(toc_path)?;
    file.write_all(contents.as_bytes())?;

    Ok(())
}
pub fn copy_rotation_to_addon_folder(
    selected_rotation: RotationConfig,
) -> Result<(), Box<dyn std::error::Error>> {
    let is_custom = selected_rotation.is_custom.unwrap_or(false);

    if is_custom {
        // For custom rotations, the files are already generated in custom_rotations folder
        // We need to copy them to the addon folder structure
        let custom_rotation_path = format!(
            "{}/{}/{}/{}",
            CUSTOM_ROTATIONS_PATH,
            selected_rotation.game,
            selected_rotation.class,
            selected_rotation.name
        );
        let addon_path = format!("{}/{}", ADDON_FOLDER_TO_COPY_PATH, selected_rotation.name);

        let src_path = Path::new(&custom_rotation_path);
        let dst_path = Path::new(&addon_path);

        if src_path.exists() {
            if let Err(e) = copy_dir_all(src_path, dst_path) {
                log::error!("Error copying custom rotation: {}", e);
                eprintln!("Error copying custom rotation directory: {}", e);
            } else {
                log::info!("Copied custom rotation files to addon structure");
                // Clean up any old files that might have been copied
                cleanup_and_rename_rotation_files(&addon_path)?;
            }
        } else {
            log::warn!(
                "Custom rotation source path does not exist: {}",
                custom_rotation_path
            );
        }
        return Ok(());
    }

    // For non-custom rotations, copy the entire directory as before
    let rotation_path = format!(
        "{}/{}/{}/{}",
        ROTATIONS_PATH, selected_rotation.game, selected_rotation.class, selected_rotation.name
    );

    let addon_path = format!("{}/{}", ADDON_FOLDER_TO_COPY_PATH, selected_rotation.name);

    let src_path = Path::new(&rotation_path);
    let dst_path = Path::new(&addon_path);

    if let Err(e) = copy_dir_all(src_path, dst_path) {
        log::error!("Error copying rotation: {}", e);
        eprintln!("Error copying directory: {}", e);
    } else {
        // Clean up old files and rename rotation.lua to Priorities.lua if needed
        cleanup_and_rename_rotation_files(&addon_path)?;
    }

    Ok(())
}
pub fn copy_addon_to_wow(settings: Settings) -> Result<(), Box<dyn std::error::Error>> {
    let wow_path = settings.wow_path.clone();
    let addon_path = format!("{}/{}", wow_path, settings.addon_name);
    let src_path = Path::new(ADDON_FOLDER_TO_COPY_PATH);
    let dst_path = Path::new(&addon_path);

    if let Err(e) = copy_addon_folder_to_wow(src_path, dst_path) {
        log::error!("Error copying rotation: {}", e);
        eprintln!("Error copying directory: {}", e);
    }

    if let Some(dependencies) = &settings.selected_rotation.dependencies {
        let is_hekili = dependencies
            .iter()
            .any(|dep| dep.to_lowercase().contains("hekili"));
        let is_hero = dependencies
            .iter()
            .any(|dep| dep.to_lowercase().contains("herorotation"));
        let is_conroc = dependencies
            .iter()
            .any(|dep| dep.to_lowercase().contains("conroc"));

        let game = settings.selected_rotation.game.clone();
        if is_hekili {
            let hekili_path = format!("hekili-{}", game);
            // Iterate over the contents of hekili_path and copy each item to addon_path
            let mut hekili_toc_file =
                File::open(format!("{}/Hekili/{}", hekili_path, "Hekili.toc"))?;
            let mut hekili_toc_contents = String::new();
            hekili_toc_file.read_to_string(&mut hekili_toc_contents)?;

            let mut hekili_toc_lines = hekili_toc_contents.lines().collect::<Vec<_>>();
            let dependencies_line = hekili_toc_lines
                .iter()
                .position(|line| line.contains("## Dependencies:"));
            if let Some(index) = dependencies_line {
                let deps = settings.addon_name.as_str();
                let deps_str = format!("## Dependencies: {}", deps);

                hekili_toc_lines.remove(index);
                hekili_toc_lines.insert(index, &deps_str);
                let new_hekili_toc_contents = hekili_toc_lines.join("\n");
                let mut hekili_toc_file =
                    File::create(format!("{}/Hekili/{}", hekili_path, "Hekili.toc"))?;
                hekili_toc_file.write_all(new_hekili_toc_contents.as_bytes())?;
            }
            copy_dir_all(Path::new(&hekili_path), Path::new(&wow_path))?;
        }
        if is_hero {
            let hero_path = format!("{}-hero", game);
            copy_dir_all(Path::new(&hero_path), Path::new(&wow_path))?;
        }
        if is_conroc {
            let conroc_path = "ConROC";

            copy_dir_all(Path::new(&conroc_path), Path::new(&wow_path))?;
        }
    }

    Ok(())
}

/// Clean up old rotation files and rename rotation.lua to Priorities.lua
fn cleanup_and_rename_rotation_files(addon_path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let rotation_lua_path = format!("{}/rotation.lua", addon_path);
    let priorities_lua_path = format!("{}/Priorities.lua", addon_path);
    let rotation_objects_path = format!("{}/rotation_objects.lua", addon_path);

    // Rename rotation.lua to Priorities.lua if it exists
    if Path::new(&rotation_lua_path).exists() {
        if let Err(e) = fs::rename(&rotation_lua_path, &priorities_lua_path) {
            log::warn!("Failed to rename rotation.lua to Priorities.lua: {}", e);
        } else {
            log::info!("Renamed rotation.lua to Priorities.lua");
        }
    }

    // Remove rotation_objects.lua since we now use the shared RStatus.lua
    if Path::new(&rotation_objects_path).exists() {
        if let Err(e) = fs::remove_file(&rotation_objects_path) {
            log::warn!("Failed to remove old rotation_objects.lua: {}", e);
        } else {
            log::info!("Removed old rotation_objects.lua (now using shared RStatus.lua)");
        }
    }

    Ok(())
}
