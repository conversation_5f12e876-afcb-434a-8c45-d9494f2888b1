import '@testing-library/jest-dom';

// Mock Tauri API
const mockTauri = {
  invoke: jest.fn(),
  emit: jest.fn(),
  listen: jest.fn(),
  shell: {
    open: jest.fn(),
  },
  dialog: {
    open: jest.fn(),
  },
};

// Mock the Tauri modules
jest.mock('@tauri-apps/api/tauri', () => mockTauri);
jest.mock('@tauri-apps/api/event', () => ({
  emit: mockTauri.emit,
  listen: mockTauri.listen,
}));
jest.mock('@tauri-apps/api/shell', () => mockTauri.shell);
jest.mock('@tauri-apps/api/dialog', () => mockTauri.dialog);

// Mock crypto.randomUUID for tests
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: () => 'test-uuid-' + Math.random().toString(36).substr(2, 9),
  },
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Extend Jest matchers
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveClass(className: string): R;
      toHaveAttribute(attr: string, value?: string): R;
    }
  }
}
