import {
  Button,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Input,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Select,
  VStack,
  ModalFooter,
} from "@chakra-ui/react";
import {
  SimpleCondition,
  CustomRotation,
  EntityType,
  PropertyType,
  ValueType,
  ComparisonOperator,
  entitySchemas,
  getValueValidation,
} from "../types";
import { useFieldArray, useForm, useFormContext } from "react-hook-form";
import { useEffect, useMemo } from "react";

interface ConditionModalProps {
  isOpen: boolean;
  onClose: () => void;
  name: string;
  selectedConditionId: string | null;
  groupId: string;
}

// Helper function to get valid options based on schema
const getValidOptions = (
  entityType: EntityType,
  propertyType?: PropertyType
) => {
  const schema = entitySchemas[entityType];

  const getPropertyOptions = (): PropertyType[] => {
    return Object.keys(schema.allowedProperties || {}) as PropertyType[];
  };

  const getValueTypeOptions = (propType: PropertyType): ValueType[] => {
    const propSchema = schema.allowedProperties?.[propType];
    return propSchema?.allowedValueTypes || [];
  };

  const getOperatorOptions = (propType: PropertyType): ComparisonOperator[] => {
    const propSchema = schema.allowedProperties?.[propType];
    return propSchema?.allowedOperators || [];
  };

  const getRequiredFields = (propType?: PropertyType): string[] => {
    if (propType) {
      const propSchema = schema.allowedProperties?.[propType];
      return propSchema?.requiredFields || [];
    }
    return schema.requiredFields || [];
  };

  return {
    propertyOptions: getPropertyOptions(),
    valueTypeOptions: propertyType ? getValueTypeOptions(propertyType) : [],
    operatorOptions: propertyType ? getOperatorOptions(propertyType) : [],
    requiredFields: getRequiredFields(propertyType),
  };
};

export const ConditionModal = ({
  isOpen,
  onClose,
  name,
  selectedConditionId,
  groupId,
}: ConditionModalProps) => {
  const { watch, setValue } = useFormContext<CustomRotation>();
  const { fields: conditionFieldArray, append } = useFieldArray({
    name: name as "sections.0.actions.0.conditions",
  });
  const watchConditions = watch(name as "sections.0.actions.0.conditions");
  const conditions = conditionFieldArray.map((condition, index) => ({
    ...condition,
    ...watchConditions[index],
  }));

  const conditionIndex = conditions.findIndex(
    (c) => c.id === selectedConditionId
  );
  const isEditing = selectedConditionId !== null && conditionIndex !== -1;

  const condition = isEditing
    ? (watch(
        `${name}.${conditionIndex}` as "sections.0.actions.0.conditions.0"
      ) as SimpleCondition)
    : undefined;

  const defaultValues: Omit<SimpleCondition, "id"> = useMemo(
    () => ({
      entityType: "PLAYER",
      propertyType: "HEALTH",
      operator: "LESS_THAN",
      valueType: "PERCENTAGE",
      value: 100,
      groupId,
    }),
    [groupId]
  );

  const {
    register,
    watch: currentValues,
    handleSubmit: formHandleSubmit,
    setValue: localSetValue,
    reset,
    trigger,
    formState: { errors },
  } = useForm<SimpleCondition>({
    defaultValues: condition ?? { ...defaultValues, id: crypto.randomUUID() },
    mode: "all",
  });

  const spells = watch("spells");
  const buffs = watch("buffs");

  // Reset form when switching between add/edit modes
  useEffect(() => {
    if (isEditing && condition) {
      reset(condition);
    } else if (!isEditing) {
      reset({ ...defaultValues, id: crypto.randomUUID() });
    }
  }, [isEditing, condition, reset]);

  const onSubmit = (data: SimpleCondition) => {
    if (isEditing && condition) {
      setValue(
        `${name}.${conditionIndex}` as "sections.0.actions.0.conditions.0",
        { ...condition, ...data }
      );
    } else {
      append({ ...data, id: crypto.randomUUID(), groupId });
    }
    onClose();
  };

  const handleClose = () => {
    reset({ ...defaultValues, id: crypto.randomUUID() });
    onClose();
  };

  const entityType = currentValues("entityType");
  const propertyType = currentValues("propertyType");
  const valueType = currentValues("valueType");
  const isNumber =
    valueType === "NUMBER" ||
    valueType === "PERCENTAGE" ||
    valueType === "SECONDS" ||
    valueType === "TIME" ||
    valueType === "ID";

  const validOptions = getValidOptions(entityType, propertyType);

  // Helper functions for input attributes
  const getMinValue = () => {
    if (valueType === "PERCENTAGE") return 0;
    if (valueType === "SECONDS" || valueType === "TIME" || valueType === "ID")
      return 0;
    return undefined;
  };

  const getMaxValue = () => {
    if (valueType === "PERCENTAGE") return 100;
    return undefined;
  };

  const getStepValue = () => {
    if (valueType === "PERCENTAGE") return 1;
    if (valueType === "SECONDS" || valueType === "TIME") return 0.1;
    return 1;
  };

  const handleEntityTypeChange = (newEntityType: EntityType) => {
    localSetValue("entityType", newEntityType);
    const newValidOptions = getValidOptions(newEntityType);

    if (newValidOptions.propertyOptions.length > 0) {
      const firstProperty = newValidOptions.propertyOptions[0];
      localSetValue("propertyType", firstProperty);

      const propertyValidOptions = getValidOptions(
        newEntityType,
        firstProperty
      );
      if (propertyValidOptions.valueTypeOptions.length > 0) {
        localSetValue("valueType", propertyValidOptions.valueTypeOptions[0]);
      }
      if (propertyValidOptions.operatorOptions.length > 0) {
        localSetValue("operator", propertyValidOptions.operatorOptions[0]);
      }
    }

    localSetValue("checkSpellId", "");
  };

  const handlePropertyTypeChange = (newPropertyType: PropertyType) => {
    localSetValue("propertyType", newPropertyType);
    const newValidOptions = getValidOptions(entityType, newPropertyType);

    if (newValidOptions.valueTypeOptions.length > 0) {
      localSetValue("valueType", newValidOptions.valueTypeOptions[0]);
    }
    if (newValidOptions.operatorOptions.length > 0) {
      localSetValue("operator", newValidOptions.operatorOptions[0]);
    }

    localSetValue("checkSpellId", "");
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          {isEditing ? "Edit Condition" : "Add Condition"}
        </ModalHeader>
        <ModalCloseButton />
        <form
          onSubmit={(e) => {
            e.stopPropagation();
            formHandleSubmit(onSubmit)(e);
          }}
        >
          <ModalBody>
            <VStack spacing={4}>
              <FormControl>
                <FormLabel>Entity Type</FormLabel>
                <Select
                  {...register("entityType")}
                  onChange={(e) =>
                    handleEntityTypeChange(e.target.value as EntityType)
                  }
                >
                  <option value="PLAYER">Player</option>
                  <option value="TARGET">Target</option>
                  <option value="SPELL">Spell</option>
                </Select>
              </FormControl>
              <FormControl>
                <FormLabel>Property Type</FormLabel>
                <Select
                  {...register("propertyType")}
                  onChange={(e) =>
                    handlePropertyTypeChange(e.target.value as PropertyType)
                  }
                >
                  {validOptions.propertyOptions.map((prop) => (
                    <option key={prop} value={prop}>
                      {prop
                        .replace(/_/g, " ")
                        .toLowerCase()
                        .replace(/\b\w/g, (l) => l.toUpperCase())}
                    </option>
                  ))}
                </Select>
              </FormControl>
              <FormControl>
                <FormLabel>Operator</FormLabel>
                <Select {...register("operator")}>
                  {validOptions.operatorOptions.map((op) => (
                    <option key={op} value={op}>
                      {op
                        .replace(/_/g, " ")
                        .toLowerCase()
                        .replace(/\b\w/g, (l) => l.toUpperCase())}
                    </option>
                  ))}
                </Select>
              </FormControl>
              <FormControl>
                <FormLabel>Value Type</FormLabel>
                <Select {...register("valueType")}>
                  {validOptions.valueTypeOptions.map((vt) => (
                    <option key={vt} value={vt}>
                      {vt
                        .replace(/_/g, " ")
                        .toLowerCase()
                        .replace(/\b\w/g, (l) => l.toUpperCase())}
                    </option>
                  ))}
                </Select>
              </FormControl>
              <FormControl isInvalid={!!errors.value}>
                <FormLabel>Value</FormLabel>
                {valueType === "BOOLEAN" ? (
                  <Select
                    {...register("value", getValueValidation(valueType))}
                    onChange={(e) => {
                      const boolValue = e.target.value === "true";
                      localSetValue("value", boolValue);
                    }}
                  >
                    <option value="">Select true or false</option>
                    <option value="true">True</option>
                    <option value="false">False</option>
                  </Select>
                ) : (
                  <Input
                    {...register("value", getValueValidation(valueType))}
                    type={isNumber ? "number" : "text"}
                    min={getMinValue()}
                    max={getMaxValue()}
                    step={getStepValue()}
                    onChange={async (e) => {
                      let value: string | number = e.target.value;
                      if (isNumber) {
                        value = Number(e.target.value);
                      }
                      localSetValue("value", value);
                      // Trigger validation to clear/show errors
                      await trigger("value");
                    }}
                  />
                )}
                <FormErrorMessage>{errors.value?.message}</FormErrorMessage>
              </FormControl>
              {validOptions.requiredFields.includes("spellId") && (
                <FormControl isInvalid={!!errors.checkSpellId}>
                  <FormLabel>Spell</FormLabel>
                  <Select
                    {...register("checkSpellId", {
                      required: "Please select a spell",
                    })}
                  >
                    <option value="">Select a spell</option>
                    {spells.map((spell) => (
                      <option key={spell.id || spell.name} value={spell.name}>
                        {spell.name}
                      </option>
                    ))}
                  </Select>
                  <FormErrorMessage>
                    {errors.checkSpellId?.message}
                  </FormErrorMessage>
                </FormControl>
              )}
              {validOptions.requiredFields.includes("buffId") && (
                <FormControl isInvalid={!!errors.buffId}>
                  <FormLabel>Buff</FormLabel>
                  <Select
                    {...register("buffId", {
                      required: "Please select a buff",
                    })}
                  >
                    <option value="">Select a buff</option>
                    {buffs
                      .filter((buff) => buff.type_ === "BUFF")
                      .map((buff) => (
                        <option key={buff.id} value={buff.id}>
                          {buff.name}
                        </option>
                      ))}
                  </Select>
                  <FormErrorMessage>{errors.buffId?.message}</FormErrorMessage>
                </FormControl>
              )}
              {validOptions.requiredFields.includes("debuffId") && (
                <FormControl isInvalid={!!errors.debuffId}>
                  <FormLabel>Debuff</FormLabel>
                  <Select
                    {...register("debuffId", {
                      required: "Please select a debuff",
                    })}
                  >
                    <option value="">Select a debuff</option>
                    {buffs
                      .filter((buff) => buff.type_ === "DEBUFF")
                      .map((buff) => (
                        <option key={buff.id} value={buff.id}>
                          {buff.name}
                        </option>
                      ))}
                  </Select>
                  <FormErrorMessage>
                    {errors.debuffId?.message}
                  </FormErrorMessage>
                </FormControl>
              )}
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button
              onClick={formHandleSubmit(onSubmit)}
              colorScheme="blue"
              mr={3}
            >
              {isEditing ? "Update" : "Add"}
            </Button>
            <Button type="button" variant="ghost" onClick={handleClose}>
              Cancel
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};
