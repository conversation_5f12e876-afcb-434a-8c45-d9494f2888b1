# WoW Rotation Lua Objects Documentation

This document describes the Lua objects and functions available for creating custom World of Warcraft rotations that match the TypeScript schema defined in `src/types.ts`.

## Backwards Compatibility

**✅ Compatible with all WoW versions:**
- **Classic Era** (Vanilla 1.15.x)
- **Cataclysm Classic** (4.4.x)
- **Retail WoW** (11.x+)

The rotation objects automatically detect the WoW version and use the appropriate API functions. Classic versions use global functions like `GetSpellInfo()`, while <PERSON><PERSON> uses namespaced functions like `C_Spell.GetSpellInfo()`.

## Overview

The rotation system consists of four main objects:
- **Player**: Functions for checking player state (health, resources, buffs, combat status)
- **Target**: Functions for checking target state (health, debuffs, existence)
- **Spell**: Functions for checking spell availability (cooldowns, charges, usability)
- **RotationHelper**: Utility functions for condition evaluation and action execution

## Player Object

### Health Functions
```lua
Player:GetHealth()          -- Returns current health points
Player:GetMaxHealth()       -- Returns maximum health points
Player:GetHealthPercent()   -- Returns health as percentage (0-100)
```

### Resource Functions (Mana, Energy, Rage, etc.)
```lua
Player:GetResource()        -- Returns current resource points
Player:GetMaxResource()     -- Returns maximum resource points
Player:GetResourcePercent() -- Returns resource as percentage (0-100)
Player:GetResourceType()    -- Returns resource type ID
```

### Combat State
```lua
Player:IsInCombat()         -- Returns true if player is in combat
Player:IsCasting()          -- Returns true if player is casting a spell
Player:IsChanneling()       -- Returns true if player is channeling a spell
```

### Buff Functions
```lua
Player:HasBuff(spellIdOrName)           -- Returns true if player has the buff
Player:GetBuffDuration(spellIdOrName)   -- Returns remaining duration in seconds
Player:GetBuffStacks(spellIdOrName)     -- Returns number of buff stacks
```

## Target Object

### Health Functions
```lua
Target:GetHealth()          -- Returns target's current health points
Target:GetMaxHealth()       -- Returns target's maximum health points
Target:GetHealthPercent()   -- Returns target's health as percentage (0-100)
```

### Existence and Validity
```lua
Target:Exists()             -- Returns true if target exists
Target:IsEnemy()            -- Returns true if target is attackable
Target:IsDead()             -- Returns true if target is dead
```

### Debuff Functions
```lua
Target:HasDebuff(spellIdOrName)           -- Returns true if target has the debuff
Target:GetDebuffDuration(spellIdOrName)   -- Returns remaining duration in seconds
Target:GetDebuffStacks(spellIdOrName)     -- Returns number of debuff stacks
```

## Spell Object

### Cooldown Functions
```lua
Spell:GetCooldown(spellIdOrName)    -- Returns remaining cooldown in seconds
Spell:IsReady(spellIdOrName)        -- Returns true if spell is off cooldown
Spell:IsUsable(spellIdOrName)       -- Returns true if spell is usable (enough resources)
Spell:IsKnown(spellIdOrName)        -- Returns true if spell is known by player
```

### Charge Functions
```lua
Spell:GetCharges(spellIdOrName)     -- Returns current number of charges
Spell:GetMaxCharges(spellIdOrName)  -- Returns maximum number of charges
```

### Range and Targeting
```lua
Spell:IsInRange(spellIdOrName, unit)  -- Returns true if spell is in range of unit
```

### Global Cooldown
```lua
Spell:GetGCD()              -- Returns remaining global cooldown in seconds
Spell:IsGCDReady()          -- Returns true if global cooldown is ready
```

## RotationHelper Object

### Condition Evaluation
```lua
RotationHelper:EvaluateCondition(condition)        -- Evaluates a single condition
RotationHelper:EvaluateConditionGroup(group)       -- Evaluates a condition group
RotationHelper:CanExecuteAction(action)             -- Checks if action can be executed
```

### Utility Functions
```lua
RotationHelper:GetTimeToNextGCD()       -- Returns time until next GCD
RotationHelper:ShouldInterrupt()        -- Returns true if target should be interrupted
RotationHelper:GetDistanceToTarget()    -- Returns approximate distance to target
```

### Debug Functions
```lua
RotationHelper:LogCondition(condition, result)  -- Logs condition evaluation
RotationHelper:LogAction(action, canExecute)    -- Logs action execution attempt
```

## Usage Examples

### Basic Health Check
```lua
if Player:GetHealthPercent() < 30 then
    -- Player health is below 30%
    -- Cast healing spell or use health potion
end
```

### Resource Management
```lua
if Player:GetResourcePercent() > 80 then
    -- Player has high resource (rage/energy/mana)
    -- Use resource-expensive abilities
end
```

### Buff/Debuff Management
```lua
-- Check if player has a specific buff
if Player:HasBuff(12345) then  -- Using spell ID
    -- Player has the buff
end

-- Check buff duration
if Player:GetBuffDuration("Battle Shout") < 30 then  -- Using spell name
    -- Buff expires in less than 30 seconds
end

-- Check target debuff
if not Target:HasDebuff("Rend") then
    -- Target doesn't have Rend debuff
    -- Cast Rend
end
```

### Spell Cooldown Checks
```lua
-- Check if spell is ready
if Spell:IsReady("Mortal Strike") and Spell:IsUsable("Mortal Strike") then
    -- Mortal Strike is off cooldown and player has enough rage
    CastSpellByName("Mortal Strike")
end

-- Check spell charges
if Spell:GetCharges("Overpower") > 0 then
    -- Overpower has charges available
    CastSpellByName("Overpower")
end
```

### Complex Condition Example
```lua
-- Example: Cast Mortal Strike if conditions are met
local canCastMortalStrike =
    Player:IsInCombat() and                    -- In combat
    Target:Exists() and                        -- Target exists
    Target:IsEnemy() and                       -- Target is enemy
    not Target:IsDead() and                    -- Target is alive
    Spell:IsReady("Mortal Strike") and         -- Spell is off cooldown
    Spell:IsUsable("Mortal Strike") and        -- Player has enough rage
    Spell:IsGCDReady() and                     -- GCD is ready
    Player:GetResourcePercent() >= 30          -- At least 30% rage

if canCastMortalStrike then
    CastSpellByName("Mortal Strike")
end
```

## Integration with Custom Rotation System

The rotation objects are designed to work seamlessly with the custom rotation system. When you create a rotation using the TypeScript interface, it gets converted to Lua code that uses these objects for condition evaluation and action execution.

### Condition Types Mapping

| TypeScript Condition | Lua Function |
|----------------------|--------------|
| `PLAYER.HEALTH` | `Player:GetHealthPercent()` |
| `PLAYER.RESOURCE` | `Player:GetResourcePercent()` |
| `PLAYER.IN_COMBAT` | `Player:IsInCombat()` |
| `PLAYER.IS_CASTING` | `Player:IsCasting() or Player:IsChanneling()` |
| `PLAYER.HAS_BUFF` | `Player:HasBuff(checkSpellId)` |
| `TARGET.HEALTH` | `Target:GetHealthPercent()` |
| `TARGET.HAS_DEBUFF` | `Target:HasDebuff(checkSpellId)` |
| `TARGET.DEBUFF_REMAINING` | `Target:GetDebuffDuration(checkSpellId)` |
| `SPELL.COOLDOWN` | `Spell:GetCooldown(checkSpellId)` |

### Action Types Mapping

| TypeScript Action | Lua Function |
|-------------------|--------------|
| `CAST` | `CastSpellByID(spellId)` |
| `WAIT` | `waitUntil = GetTime() + waitSeconds` |
| `START_SECTION` | `currentSectionId = startSectionId` |

## World of Warcraft API Reference

This system uses the official World of Warcraft API functions. For complete documentation, see:
https://warcraft.wiki.gg/wiki/World_of_Warcraft_API

Key API functions used:
- `UnitHealth()`, `UnitHealthMax()`, `UnitPower()`, `UnitPowerMax()`
- `UnitAffectingCombat()`, `UnitCastingInfo()`, `UnitChannelInfo()`
- `AuraUtil.FindAuraByName()`, `GetSpellInfo()`, `GetSpellCooldown()`
- `IsUsableSpell()`, `IsSpellKnown()`, `GetSpellCharges()`, `IsSpellInRange()`
- `CastSpellByID()`, `CastSpellByName()`
