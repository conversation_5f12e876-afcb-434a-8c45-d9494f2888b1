import { VStack, FormControl, FormLabel, Input, Select } from "@chakra-ui/react";
import { getClasses, getSpecs } from "../utils";
import { useFormContext } from "react-hook-form";


export const BasicInfoSection = () => {
  const { register, watch, setValue } = useFormContext()
  const formGame = watch("game")
  const formClass = watch("class")

  const availableClasses = getClasses(formGame)
  const availableSpecs = getSpecs(formGame, formClass)

  const handleGameChange = (game: string) => {
    setValue("game", game);
    setValue("class", "");
    setValue("spec", "");
  };

  const handleClassChange = (class_: string) => {
    setValue("class", class_);
    setValue("spec", "");
  };

  return (
    <VStack spacing={4} align="stretch">
      <FormControl isRequired>
        <FormLabel>Rotation Name</FormLabel>
        <Input {...register("name")} placeholder="Enter rotation name" />
      </FormControl>

      <FormControl isRequired>
        <FormLabel>Game</FormLabel>
        <Select
          {...register("game")}
          value={formGame || ""}
          onChange={(e) => handleGameChange(e.target.value)}
          placeholder="Select game"
        >
          <option value="retail">Retail</option>
          <option value="classic">Classic (Cata)</option>
          <option value="era">Era</option>
          <option value="sod">Season of Discovery</option>
        </Select>
      </FormControl>

      <FormControl isRequired>
        <FormLabel>Class</FormLabel>
        <Select
          {...register("class")}
          value={formClass || ""}
          onChange={(e) => handleClassChange(e.target.value)}
          placeholder="Select class"
          isDisabled={!formGame}
        >
          {availableClasses.map((class_) => (
            <option key={class_} value={class_}>
              {class_}
            </option>
          ))}
        </Select>
      </FormControl> 

      <FormControl isRequired>
        <FormLabel>Specialization</FormLabel>
        <Select
          {...register("spec")}
          placeholder="Select specialization"
          isDisabled={!formClass}
        >
          {availableSpecs.map((spec) => (
            <option key={spec} value={spec}>
              {spec}
            </option>
          ))}
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel>Author</FormLabel>
        <Input {...register("author")} placeholder="Enter author name" />
      </FormControl>

      <FormControl>
        <FormLabel>Version</FormLabel>
        <Input {...register("version")} placeholder="Enter version" />
      </FormControl>
    </VStack>
  );
}; 