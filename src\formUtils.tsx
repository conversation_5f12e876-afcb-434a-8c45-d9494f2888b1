import { ConditionGroup } from "./types";

import { CustomRotationCondition } from "./types";

// Helper functions to work with the hierarchical structure
export const updateConditionInList = (
    conditions: CustomRotationCondition[], 
    conditionId: string, 
    updatedCondition: CustomRotationCondition
  ): CustomRotationCondition[] => {
    return conditions.map(condition => {
      if (condition.id === conditionId) {
        return updatedCondition;
      }
      if (condition.entityType === "GROUP") {
        return {
          ...condition,
          conditions: updateConditionInList((condition as ConditionGroup).conditions, conditionId, updatedCondition)
        } as ConditionGroup;
      }
      return condition;
    });
  };
  
export const deleteConditionFromList = (
    conditions: CustomRotationCondition[], 
    conditionId: string
  ): CustomRotationCondition[] => {
  
    if (conditions.some(condition => condition.id === conditionId)) {
      return conditions.filter(condition => condition.id !== conditionId);
    }
  
    return conditions
      .map(condition => {
        if (condition.entityType === "GROUP") {
          return {
            ...condition,
            conditions: deleteConditionFromList((condition as ConditionGroup).conditions, conditionId)
          } as ConditionGroup;
        }
        return condition;
      });
  };
  
export const addConditionToList = (
    conditions: CustomRotationCondition[], 
    newCondition: CustomRotationCondition
  ): CustomRotationCondition[] => {
    if (!newCondition.groupId || conditions.length === 0) {
      // At root level
      return [...conditions, newCondition];
    }
    
    return conditions.map(condition => {
      if (condition.id === newCondition.groupId) {
        return {
          ...condition,
          conditions: [...(condition as ConditionGroup).conditions, newCondition]
        } as ConditionGroup;
      }
      if (condition.entityType === "GROUP") {
        return {
          ...condition,
          conditions: addConditionToList((condition as ConditionGroup).conditions, newCondition)
        } as ConditionGroup;
      }
      return condition;
    });
  };
  