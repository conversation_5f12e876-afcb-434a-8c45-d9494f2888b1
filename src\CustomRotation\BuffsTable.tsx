import {
  <PERSON>,
  <PERSON>ing,
  But<PERSON>,
  <PERSON>,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Input,
  Select,
} from "@chakra-ui/react";
import { useFormContext, useFieldArray } from "react-hook-form";
import { useState, useEffect } from "react";
import { CustomRotation, CustomRotationBuff } from "../types";

interface BuffsTableProps {
  size?: "sm" | "md" | "lg";
  compact?: boolean;
}

export const BuffsTable = ({
  size = "md",
  compact = false,
}: BuffsTableProps) => {
  const { register, watch, setValue, control } =
    useFormContext<CustomRotation>();

  const {
    fields: buffs,
    append: appendBuff,
    remove: removeBuff,
  } = useFieldArray({
    control,
    name: "buffs",
  });

  const watchBuffs = watch("buffs");
  const controlledBuffs = buffs.map((buff, index) => ({
    ...buff,
    ...watchBuffs[index],
  }));

  // Local state to track input values for each buff
  const [inputValues, setInputValues] = useState<{ [key: string]: string }>({});

  // Initialize input values when buffs change
  useEffect(() => {
    const newInputValues: { [key: string]: string } = {};
    controlledBuffs.forEach((buff) => {
      if (!inputValues[buff.id]) {
        newInputValues[buff.id] = buff.spell_ids.join(",");
      }
    });
    if (Object.keys(newInputValues).length > 0) {
      setInputValues((prev) => ({ ...prev, ...newInputValues }));
    }
  }, [controlledBuffs.length]);

  const handleBuffIdsChange = (
    buffId: string,
    index: number,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value;

    // Format the input in real-time: replace spaces with commas
    const formattedValue = value.replace(/\s+/g, ",");

    // Update local state to maintain input value
    setInputValues((prev) => ({ ...prev, [buffId]: formattedValue }));

    // Parse and clean the IDs for form state
    const cleanedIds = formattedValue
      // Split by comma
      .split(",")
      // Remove empty strings and trim
      .map((id: string) => id.trim())
      .filter((id: string) => id !== "")
      // Convert to numbers and filter out invalid ones
      .map((id: string) => {
        const parsedId = parseInt(id);
        return isNaN(parsedId) ? 0 : parsedId;
      })
      .filter((id: number) => id !== 0);

    setValue(`buffs.${index}.spell_ids`, cleanedIds);
  };

  return (
    <Box>
      <Heading size={compact ? "sm" : "md"} mb={4}>
        Buffs/Debuffs
      </Heading>
      <Button
        onClick={() =>
          appendBuff({
            id: crypto.randomUUID(),
            name: "",
            type_: "BUFF",
            spell_ids: [],
          })
        }
        mb={4}
        size={compact ? "sm" : "md"}
      >
        Add Buff/Debuff
      </Button>
      <Table variant="simple" size={size}>
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Type</Th>
            <Th>Spell IDs</Th>
            <Th width={compact ? "30px" : "50px"}></Th>
          </Tr>
        </Thead>
        <Tbody>
          {controlledBuffs?.map((buff: CustomRotationBuff, index: number) => (
            <Tr key={buff.id}>
              <Td>
                <Input
                  {...register(`buffs.${index}.name`)}
                  placeholder="Buff/Debuff name"
                  size={compact ? "sm" : "md"}
                />
              </Td>
              <Td>
                <Select
                  {...register(`buffs.${index}.type_`)}
                  size={compact ? "sm" : "md"}
                >
                  <option value="BUFF">Buff</option>
                  <option value="DEBUFF">Debuff</option>
                </Select>
              </Td>
              <Td>
                <Input
                  value={inputValues[buff.id] || ""}
                  onChange={(e) => handleBuffIdsChange(buff.id, index, e)}
                  placeholder="Comma-separated spell IDs"
                  size={compact ? "sm" : "md"}
                />
              </Td>
              <Td>
                <Button
                  size={compact ? "xs" : "sm"}
                  colorScheme="red"
                  variant="ghost"
                  onClick={() => removeBuff(index)}
                >
                  ×
                </Button>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};
