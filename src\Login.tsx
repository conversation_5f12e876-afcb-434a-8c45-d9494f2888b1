import { Button, Flex, Heading, Input, Text, VStack } from "@chakra-ui/react";
import "./App.css";
import { FormEvent, useState } from "react";
import { useSettingsStore } from "./stores/store";
import { activate } from "./Initialize";

export const Login = ({ error: activationError }: { error: string | null }) => {
  const [key, setKey] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(activationError);
  const { setActivationKey } = useSettingsStore();

  const handleSubmit = async (e: FormEvent<HTMLDivElement> | undefined) => {
    e?.preventDefault();
    setError("");
    setLoading(true);
    const success = await activate(key);

    if (success) {
      setActivationKey(key);
    } else {
      setError("Invalid activation key");
    }
    setLoading(false);
  };

  return (
    <Flex alignItems="center" justifyContent="center" height="100vh">
      <VStack
        as="form"
        onSubmit={(e) => handleSubmit(e)}
        align="center"
        justify="center"
      >
        <Heading className="title" size="lg">
          OptiStrike Rotations 🔥
        </Heading>
        <Input
          mt={4}
          placeholder="Enter activation key"
          value={key}
          onChange={(e) => setKey(e.target.value)}
        />
        {error && <Text color="red.500">{error}</Text>}
        <Button
          disabled={loading}
          type="submit"
          colorScheme="purple"
          width="full"
          height="32px"
        >
          Activate
        </Button>
      </VStack>
    </Flex>
  );
};
