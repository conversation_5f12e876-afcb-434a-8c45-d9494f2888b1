use std::fs::{self};

use crate::{
    constants::KEYBINDS_PATH,
    keyboard::get_available_keys,
    types::{RotationConfig, SavedKeybind, SavedRotationKeybinds, SelectedRotation},
};

fn get_saved_keybinds() -> Result<Vec<SavedRotationKeybinds>, Box<dyn std::error::Error>> {
    let contents = fs::read_to_string(KEYBINDS_PATH);
    let keybinds = match contents {
        Ok(content) if !content.is_empty() => {
            let keybinds: Vec<SavedRotationKeybinds> = serde_json::from_str(&content)?;
            keybinds
        }
        _ => Vec::new(),
    };

    Ok(keybinds)
}

fn assign_keybinds(
    spell_names: Vec<String>,
    current_keybinds: Vec<SavedKeybind>,
) -> Result<Vec<SavedKeybind>, Box<dyn std::error::Error>> {
    let mut new_keybinds: Vec<SavedKeybind> = Vec::new();
    let mut available_keys = get_available_keys()?;

    for spell in spell_names {
        let saved_key = current_keybinds.iter().find(|x| x.name == spell);
        if let Some(saved_key) = saved_key {
            new_keybinds.push(saved_key.clone());
        } else {
            // Find a key from available_keys that is not in current_keybinds
            if let Some(index) = available_keys.iter().position(|(key, _, m)| {
                !current_keybinds.iter().any(|x| {
                    x.key == *key && (x.modifier.is_some() && x.modifier.as_deref().unwrap() == *m)
                })
            }) {
                // Create a new SavedKeybind
                let new_key = available_keys.remove(index);
                let modifier = if new_key.2.is_empty() {
                    None
                } else {
                    Some(new_key.2)
                };
                let new_saved_keybind = SavedKeybind {
                    name: spell.clone(),
                    key: new_key.0,
                    key_code: new_key.1 as i32,
                    modifier: modifier,
                };

                // Add the new keybind to the list
                new_keybinds.push(new_saved_keybind);
            } else {
                let new_saved_keybind = SavedKeybind {
                    name: spell.clone(),
                    key: String::from(""),
                    key_code: 0,
                    modifier: None,
                };
                new_keybinds.push(new_saved_keybind);
            }
        }
    }

    Ok(new_keybinds)
}
fn update_saved_keybinds(
    new_keybinds: Vec<SavedRotationKeybinds>,
) -> Result<(), Box<dyn std::error::Error>> {
    // Serialize new_keybinds to JSON string
    let contents = serde_json::to_string_pretty(&new_keybinds)?;

    fs::write(KEYBINDS_PATH, contents)?;

    Ok(())
}
pub fn merge_keybinds(
    new_rotations: Vec<RotationConfig>,
) -> Result<(), Box<dyn std::error::Error>> {
    let current_keybinds = get_saved_keybinds().unwrap_or(vec![]);
    let mut new_keybinds: Vec<SavedRotationKeybinds> = Vec::new();

    for rotation in new_rotations {
        let rotation_id = format!("{}.{}.{}", rotation.game, rotation.class, rotation.name)
            .replace(' ', "_")
            .to_lowercase();
        let mut rotation_spell_names: Vec<String> = Vec::new();

        rotation_spell_names.extend(rotation.spells.iter().map(|spell| spell.name.clone()));
        rotation_spell_names.extend(rotation.macros.iter().map(|m| m.name.clone()));
        // Check if there is saved keybinds for this rotation
        let old_keybinds = current_keybinds
            .iter()
            .find(|x| x.rotation_id == rotation_id);

        let assigned_keybinds = if let Some(keybinds) = old_keybinds {
            assign_keybinds(rotation_spell_names, keybinds.keybinds.clone())?
        } else {
            assign_keybinds(rotation_spell_names, vec![])?
        };

        new_keybinds.push(SavedRotationKeybinds {
            rotation_id,
            keybinds: assigned_keybinds,
        });
    }

    // Save the new keybinds
    update_saved_keybinds(new_keybinds)?;

    Ok(())
}

pub fn get_rotation_keybinds(rotation: SelectedRotation) -> SavedRotationKeybinds {
    let keybinds = get_saved_keybinds().unwrap_or(vec![]);
    let rotation_id = format!("{}.{}.{}", rotation.game, rotation.class, rotation.rotation)
        .replace(' ', "_")
        .to_lowercase();

    println!("Looking for keybinds with rotation_id: {}", rotation_id);
    println!(
        "Available keybind rotation_ids: {:?}",
        keybinds.iter().map(|k| &k.rotation_id).collect::<Vec<_>>()
    );

    let default_keybinds = SavedRotationKeybinds {
        rotation_id: String::new(),
        keybinds: vec![],
    };
    let rotation_keybinds = keybinds
        .iter()
        .find(|x| x.rotation_id.to_lowercase() == rotation_id.to_lowercase())
        .unwrap_or(&default_keybinds);

    if rotation_keybinds.rotation_id.is_empty() {
        println!("No keybinds found for rotation_id: {}", rotation_id);
    } else {
        println!(
            "Found keybinds for rotation_id: {}",
            rotation_keybinds.rotation_id
        );
    }

    rotation_keybinds.clone()
}

pub fn save_new_keybinds(
    new_keybinds: SavedRotationKeybinds,
) -> Result<(), Box<dyn std::error::Error>> {
    let current_keybinds = get_saved_keybinds().unwrap_or(vec![]);

    let mut updated_keybinds = current_keybinds.clone();

    // Find and replace existing keybinds or add new one
    if let Some(index) = updated_keybinds
        .iter()
        .position(|x| x.rotation_id.to_lowercase() == new_keybinds.rotation_id.to_lowercase())
    {
        updated_keybinds[index] = new_keybinds;
    } else {
        updated_keybinds.push(new_keybinds);
    }

    // Save the new keybinds
    update_saved_keybinds(updated_keybinds)?;
    println!("Updated keybinds");
    Ok(())
}
