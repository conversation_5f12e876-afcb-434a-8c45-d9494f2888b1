// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]
use addon_loader::clean_wow_addons;
use constants::ADDON_FOLDER_TO_COPY_PATH;
use constants::DRY_CORE_FILE_NAME;
use flexi_logger::Age;
use flexi_logger::Cleanup;
use flexi_logger::Criterion;
use flexi_logger::FileSpec;
use flexi_logger::Naming;
use keybinds::get_rotation_keybinds;
use keybinds::merge_keybinds;
use keybinds::save_new_keybinds;
use log::{error, info};
use once_cell::sync::Lazy;
use rand::distributions::Alphanumeric;
use rotation::copy_addon_to_wow;
use rotation::copy_rotation_to_addon_folder;
use rotation::generate_toc_file;
use rotation::hydrate_core_lua;
use std::env;
use std::process::Command;
use tauri::{Manager, Window};
mod addon_loader;
mod constants;
mod fs_helpers;
mod keybinds;
mod keyboard;
mod pixel_finder;
mod rotation;
mod settings;
mod types;
use rand::Rng;
use serde_json::from_str;
use serde_json::json;
use std::collections::HashMap;
use std::fs;
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::{Duration, Instant};
use types::ConfigInput;
use types::KeybindPixel;
use types::OptionalPayload;
use types::RotationConfig;
use types::SavedRotationKeybinds;
use types::SelectedRotation;
use types::{Settings, StatusPayload};
extern crate dotenvy;

use dotenvy_macro::dotenv;

use flexi_logger::{Duplicate, Logger, WriteMode};

use crate::fs_helpers::copy_dir_all;
use std::path::Path;

static ROTATIONS: Lazy<Mutex<Vec<RotationConfig>>> = Lazy::new(|| Mutex::new(Vec::new()));

struct LastMessage {
    message: String,
}

static LAST_MESSAGE: Lazy<Mutex<LastMessage>> = Lazy::new(|| {
    Mutex::new(LastMessage {
        message: String::new(),
    })
});

#[derive(Debug, thiserror::Error)]
enum Error {
    #[error("Failed to read file: {0}")]
    Io(#[from] std::io::Error),
    #[error("File is not valid utf8: {0}")]
    Utf8(#[from] std::string::FromUtf8Error),
}

// we must also implement serde::Serialize
impl serde::Serialize for Error {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::ser::Serializer,
    {
        serializer.serialize_str(self.to_string().as_ref())
    }
}

const DEFAULT_VARIANCE_MIN: u64 = 0;
const DEFAULT_VARIANCE_MAX: u64 = 100;

fn should_skip_downloads() -> bool {
    // Skip downloads in debug mode or if .no_download file exists
    cfg!(debug_assertions) || std::path::Path::new(".no_download").exists()
}

fn load_local_core_file() -> Result<(), Box<dyn std::error::Error>> {
    log("Loading core file from local directory");

    // Check if local core file exists in root directory
    let local_core_path = "dev_core.lua";
    let dest_path = format!("{}/{}", ADDON_FOLDER_TO_COPY_PATH, DRY_CORE_FILE_NAME);

    if std::path::Path::new(local_core_path).exists() {
        fs::copy(local_core_path, &dest_path)?;
        log("Loaded core file from local directory");
    } else {
        // Create a minimal core file if none exists
        error!("Local core file not found");
    }

    Ok(())
}

fn clean_addon_files_folder() -> Result<(), std::io::Error> {
    log("Cleaning addon files folder...");
    let dir_path = ADDON_FOLDER_TO_COPY_PATH;
    for entry in fs::read_dir(dir_path)? {
        let entry = entry?;
        let path = entry.path();
        if path.is_file() && path.file_name().unwrap() != DRY_CORE_FILE_NAME {
            fs::remove_file(path)?;
        } else if path.is_dir() {
            fs::remove_dir_all(path)?;
        }
    }
    Ok(())
}

pub fn log(msg: &str) {
    let mut last_message = LAST_MESSAGE.lock().unwrap();
    if msg == last_message.message {
        return;
    }
    last_message.message = msg.to_string();
    info!("{}", msg);
}

pub fn log_error(msg: &str) {
    let mut last_message = LAST_MESSAGE.lock().unwrap();
    if msg == last_message.message {
        return;
    }
    last_message.message = msg.to_string();
    error!("{}", msg);
}

#[tauri::command]
fn save_settings(window: Window, payload: String) -> Settings {
    log(&format!("Saving settings: {}", payload));
    let mut current_settings = settings::read_settings_from_file();
    let old_name = current_settings.addon_name.clone();

    if payload != "" {
        let new_settings: ConfigInput = from_str(&payload).unwrap();
        current_settings.wow_path = new_settings.wow_path;
        current_settings.selected_rotation = new_settings.selected_rotation;
    }

    settings::write_settings_to_file(&current_settings).unwrap();

    let _ = clean_addon_files_folder();

    let ace_libs_source_path = Path::new("libs").join("Ace3");
    let ace_libs_dest_path = Path::new(ADDON_FOLDER_TO_COPY_PATH)
        .join("Libs")
        .join("Ace3");

    if let Some(parent) = ace_libs_dest_path.parent() {
        if !parent.exists() {
            if let Err(e) = fs::create_dir_all(parent) {
                log_error(&format!(
                    "Failed to create parent directory for Ace3 libs {}: {}",
                    parent.display(),
                    e
                ));
            }
        }
    }

    log(&format!(
        "Copying Ace3 libraries from {} to {}",
        ace_libs_source_path.display(),
        ace_libs_dest_path.display()
    ));
    if ace_libs_source_path.exists() {
        if let Err(e) = copy_dir_all(&ace_libs_source_path, &ace_libs_dest_path) {
            log_error(&format!("Failed to copy Ace3 libraries: {}", e));
        } else {
            log("Ace3 libraries copied successfully.");
        }
    } else {
        log_error(&format!(
            "Ace3 source directory not found: {}",
            ace_libs_source_path.display()
        ));
    }

    let _ = clean_wow_addons(
        &current_settings.wow_path,
        &old_name,
        &current_settings.addon_name,
    );
    // Clean up old rotation files first
    cleanup_old_rotation_files();

    // Generate Lua files from custom rotations before copying
    let _ = custom_rotations::generate_lua_from_custom_rotations();

    // Copy RStatus to Libs folder for all rotations
    if let Err(e) = custom_rotations::copy_rstatus_to_libs() {
        log_error(&format!("Failed to copy RStatus to Libs: {}", e));
    }

    let _ = hydrate_core_lua(current_settings.selected_rotation.clone());
    let _ = generate_toc_file(current_settings.clone());
    let _ = copy_rotation_to_addon_folder(current_settings.selected_rotation.clone());
    let _ = copy_addon_to_wow(current_settings.clone());

    window
        .emit(
            "status",
            StatusPayload {
                status: "Settings saved - Reload WoW!".into(),
                running: false,
            },
        )
        .unwrap();

    let mut print_settings = current_settings.clone();
    print_settings.activation_key = None;
    log(&format!("Saved config: {:?}", print_settings));

    return current_settings;
}

#[tauri::command]
fn save_activation_key(payload: String) {
    let mut current_settings = settings::read_settings_from_file();
    current_settings.activation_key = Some(payload);
    settings::write_settings_to_file(&current_settings).unwrap();
    log("Saved activation key");
}

#[tauri::command]
fn get_activation_key() -> Option<String> {
    let current_settings = settings::read_settings_from_file();
    return current_settings.activation_key;
}

#[tauri::command]
fn generate_custom_rotation_lua() -> Result<String, String> {
    match custom_rotations::generate_lua_from_custom_rotations() {
        Ok(_) => Ok("Successfully generated Lua files from custom rotations".to_string()),
        Err(e) => Err(format!("Failed to generate Lua files: {}", e)),
    }
}

#[tauri::command]
fn save_app_settings(payload: String) -> Settings {
    let mut current_settings = settings::read_settings_from_file();
    let new_settings: Settings = from_str(&payload).unwrap();
    current_settings.rate_limit = new_settings.rate_limit;
    current_settings.key_limit = new_settings.key_limit;
    current_settings.addon_name = new_settings.addon_name;
    settings::write_settings_to_file(&current_settings).unwrap();
    let mut print_settings = current_settings.clone();
    print_settings.activation_key = None;
    log(&format!("Saved app settings: {:?}", print_settings));
    return current_settings.clone();
}

#[tauri::command]
fn get_settings() -> Settings {
    let current_settings = settings::read_settings_from_file();

    let mut settings = current_settings.clone();
    settings.selected_rotation = current_settings.selected_rotation;
    settings.wow_path = current_settings.wow_path;
    settings.addon_name = current_settings.addon_name;
    settings.activation_key = current_settings.activation_key;
    settings.rotations = ROTATIONS.lock().unwrap().clone();
    settings.rate_limit = current_settings.rate_limit;
    settings.key_limit = current_settings.key_limit;

    return settings;
}

#[tauri::command]
fn get_rotations() -> Vec<RotationConfig> {
    return ROTATIONS.lock().unwrap().clone();
}

#[tauri::command]
fn download_rotations() -> Result<(), Box<dyn std::error::Error>> {
    let _downloaded = rotation::download_rotations()?;

    /*     let community_rotations = rotation::download_community_rotations();
    if community_rotations.is_err() {
        return Err(community_rotations.err().unwrap());
    } */

    let rotations = rotation::get_rotations().unwrap();
    *ROTATIONS.lock().unwrap() = rotations;
    // Update keybinds
    merge_keybinds(ROTATIONS.lock().unwrap().clone())?;

    Ok(())
}

#[tauri::command]
fn download_core_file() -> Result<(), Box<dyn std::error::Error>> {
    let dst = format!("{}/{}", ADDON_FOLDER_TO_COPY_PATH, DRY_CORE_FILE_NAME);
    let _downloaded = rotation::download("core.lua", &dst)?;
    Ok(())
}

fn emit_status(window: &Window, status: &str) {
    window
        .emit(
            "status",
            OptionalPayload {
                status: status.into(),
                running: None,
            },
        )
        .unwrap();
}

#[tauri::command]
fn install_driver() -> Result<(), String> {
    let output = Command::new("LogitechDriver.exe")
        .output()
        .map_err(|e| format!("Error installing LogitechDriver.exe: {}", e))?;

    if output.status.success() {
        let s = String::from_utf8_lossy(&output.stdout);
        println!("Driver installed: {}", s);
    } else {
        let s = String::from_utf8_lossy(&output.stderr);
        println!("Error: {}", s);
    }

    Ok(())
}

#[tauri::command]
fn initialize(window: Window) -> String {
    log("Checking driver");
    let lib_result = keyboard::load_and_init();
    if let Err(e) = lib_result {
        emit_status(&window, &format!("{}", e));
        log_error(&format!("{}", e));
        return e;
    }

    log("Cleaning local addon files");
    if let Err(e) = addon_loader::clean_local_addon_files() {
        log_error(&format!("{}", e));
        emit_status(&window, &format!("Error initializing: {}", e));
        return "Error initializing addon files.\nPlease run OptiStrike as admin".to_string();
    }

    if should_skip_downloads() {
        log("Skipping downloads (development mode)");
        emit_status(&window, "Skipping downloads (development mode)");

        // Load local core file
        if let Err(e) = load_local_core_file() {
            emit_status(&window, &format!("Error loading local core file: {}", e));
            log_error(&format!("Error loading local core file: {}", e));
            return "Error loading local core file.\nPlease check if src-tauri/dry_core.lua exists"
                .to_string();
        }

        // Try to load existing rotations if available
        if let Ok(rotations) = rotation::get_rotations() {
            *ROTATIONS.lock().unwrap() = rotations;
            log("Loaded existing rotations from local files");
        } else {
            log("No existing rotations found, continuing without downloads");
        }
    } else {
        log("Downloading core API");
        if let Err(e) = download_core_file() {
            emit_status(&window, &format!("{}", e));
            log_error(&format!("Error initializing: {}", e));
            return "Error while downloading OptriStrike API.\nPlease restart the application"
                .to_string();
        }

        log("Downloading rotations");
        if let Err(e) = download_rotations() {
            emit_status(&window, &format!("{}", e));
            log_error(&format!("Error initializing: {}", e));
            return "Error while downloading rotations.\nPlease restart the application"
                .to_string();
        }
    }

    if should_skip_downloads() {
        log("Initialization complete (downloads skipped)");
    } else {
        log("Downloads complete");
    }

    // Load custom rotations into the global list
    if let Err(e) = custom_rotations::update_global_rotations_list() {
        log_error(&format!("Failed to load custom rotations: {}", e));
    }

    let mut settings = settings::read_settings_from_file();
    settings.activation_key = None;
    log(&format!("Settings: {:?}", settings));

    emit_status(&window, "Initialized");
    return "initialized".to_string();
}

#[tauri::command]
fn get_keybinds(payload: String) -> SavedRotationKeybinds {
    println!("Getting keybinds from script: {:?}", payload);
    let current_rotation: SelectedRotation = from_str(&payload).unwrap();
    println!(
        "Parsed rotation: game={}, class={}, rotation={}",
        current_rotation.game, current_rotation.class, current_rotation.rotation
    );
    let rotation_keybinds = get_rotation_keybinds(current_rotation);
    println!(
        "Found keybinds: rotation_id={}, keybinds_count={}",
        rotation_keybinds.rotation_id,
        rotation_keybinds.keybinds.len()
    );
    return rotation_keybinds;
}

#[tauri::command]
fn set_keybinds(window: Window, payload: String) -> Result<SavedRotationKeybinds, String> {
    let data: SavedRotationKeybinds =
        from_str(&payload).map_err(|e| format!("Failed to parse keybinds: {}", e))?;
    let _ =
        save_new_keybinds(data.clone()).map_err(|e| format!("Failed to save keybinds: {}", e))?;
    println!("Saved keybinds");
    save_settings(window, "".to_string());
    Ok(data)
}

#[tauri::command]
fn start_bot(window: Window) -> &'static str {
    let mut running = false;
    let mut send_active_msg = true;
    let stop = Arc::new(Mutex::new(false));
    let stop_clone = Arc::clone(&stop);

    let user_settings = settings::read_settings_from_file();
    if user_settings.wow_path.is_empty() {
        log_error("Could not get wow path, please update wow Addon folder path");
        return "Could not get wow path, please update wow Addon folder path";
    }

    let keybinds = get_rotation_keybinds(SelectedRotation {
        rotation: user_settings.selected_rotation.name.clone(),
        class: user_settings.selected_rotation.class.clone(),
        game: user_settings.selected_rotation.game.clone(),
    })
    .keybinds;

    let spells = user_settings.selected_rotation.spells.clone();
    let macros = user_settings.selected_rotation.macros.clone();

    // Combine spells and macros, then combine the objects from keybinds with same name
    let mut combined_spells_and_macros = HashMap::new();
    for spell in spells.iter() {
        let pixel_keybind = KeybindPixel {
            name: spell.name.clone(),
            pixel: spell.pixel.expect("").clone(),
            key: None,
            key_code: 0,
            modifier: None,
        };
        combined_spells_and_macros.insert(spell.name.clone(), pixel_keybind);
    }
    for macro_ in macros.iter() {
        let pixel_keybind = KeybindPixel {
            name: macro_.name.clone(),
            pixel: macro_.pixel.expect("").clone(),
            key: None,
            key_code: 0,
            modifier: None,
        };
        combined_spells_and_macros.insert(macro_.name.clone(), pixel_keybind);
    }
    let mut combined_keybinds = HashMap::new();

    // find the keybind from keybinds with the same name as the combined_spells_and_macros
    for pixel_keybind in combined_spells_and_macros.values() {
        if let Some(keybind) = keybinds.iter().find(|k| k.name == pixel_keybind.name) {
            combined_keybinds.insert(
                pixel_keybind.name.clone(),
                KeybindPixel {
                    name: pixel_keybind.name.clone(),
                    pixel: pixel_keybind.pixel.clone(),
                    key: Some(keybind.key.clone()),
                    key_code: keybind.key_code,
                    modifier: keybind.modifier.clone(),
                },
            );
        }
    }

    let lib_result = keyboard::load_and_init();

    if let Ok(lib) = lib_result {
        let target_frame_duration = Duration::from_secs(1) / user_settings.rate_limit;
        let target_keypress_duration = Duration::from_secs(1) / user_settings.key_limit;
        let mut last_pressed = Instant::now();

        std::thread::spawn(move || loop {
            let stop_event = Arc::clone(&stop);
            let start_time = Instant::now();

            if *stop_clone.lock().unwrap() {
                log("Stopping OptiStrike");
                window
                    .emit(
                        "status",
                        OptionalPayload {
                            status: "Stopped".into(),
                            running: Some(false),
                        },
                    )
                    .unwrap();
                break;
            }

            window.listen("stop", move |_event| {
                let mut stop_guard = stop_event.lock().unwrap();
                *stop_guard = true;
            });

            if !running {
                log("Starting OptiStrike");
                window
                    .emit(
                        "status",
                        OptionalPayload {
                            status: "Started".into(),
                            running: Some(true),
                        },
                    )
                    .unwrap();
                running = true;
            }

            match pixel_finder::find_window_by_title("World of Warcraft") {
                Ok(hwnd) => {
                    let pixels = pixel_finder::read_pixels(
                        hwnd,
                        combined_keybinds.values().cloned().collect(),
                    );
                    let pixel_data_json = json!(pixels);
                    window.emit("data", pixel_data_json).unwrap();

                    if send_active_msg {
                        log("World of Warcraft active");
                        send_active_msg = false;
                        emit_status(&window, "OptiStrike activated")
                    }

                    let paused = rotation::check_pixel(&pixels, "Paused");

                    if paused {
                        println!("Paused");
                        log("Paused");
                        emit_status(&window, "Paused");
                        continue;
                    }

                    let addon_detected = rotation::check_pixel(&pixels, "Started");

                    if !addon_detected {
                        log("Wow found, but did not detect addon");
                        emit_status(&window, "Wow found, but did not detect addon");
                        continue;
                    }

                    let in_combat = rotation::check_pixel(&pixels, "InCombat");

                    if !in_combat {
                        log("Waiting for combat...");
                        emit_status(&window, "Waiting for combat...");
                        continue;
                    }

                    let pixel_to_cast = rotation::pixel_to_cast(&pixels);
                    let key_code = pixel_to_cast.as_ref().and_then(|p| Some(p.key_code));
                    let key = pixel_to_cast.as_ref().and_then(|p| p.key.clone());

                    if pixel_to_cast.is_none() {
                        continue;
                    }

                    if key_code.is_none() || key.is_none() {
                        log(&format!("No key bound for {}", pixel_to_cast.unwrap().name));
                        continue;
                    }

                    let kc = key_code.unwrap();
                    let unicode_key = key.unwrap();

                    if kc == 0 || unicode_key.is_empty() {
                        log(&format!("No key bound for {}", pixel_to_cast.unwrap().name));
                        continue;
                    }

                    let pixel = pixel_to_cast.unwrap();

                    let elapsed = last_pressed.elapsed();
                    let deviation: Duration = Duration::from_millis(
                        rand::thread_rng().gen_range(DEFAULT_VARIANCE_MIN..DEFAULT_VARIANCE_MAX),
                    );
                    let should_press = elapsed >= target_keypress_duration + deviation;

                    if should_press {
                        println!(
                            "Casting: {}, KeyCode: {:?}, Key: {:?}",
                            pixel.name, kc, unicode_key
                        );
                        //enigo.key_click(Key::Raw(pixel.key));
                        keyboard::press_key(pixel.clone(), &lib);
                        emit_status(&window, &format!("Casting {}", pixel.name));
                        last_pressed = Instant::now();
                    }
                }
                Err(pixel_finder::WindowError::NotFound) => {
                    send_active_msg = true;
                    log("World of Warcraft window not active");
                    emit_status(&window, "World of Warcraft not active");
                }
            }

            let elapsed = start_time.elapsed();
            if elapsed < target_frame_duration {
                let sleep_duration = target_frame_duration - elapsed;
                thread::sleep(sleep_duration);
            }
        });
    } else {
        log_error("Failed to initialize Logitech driver. Please install LogitechDriver.exe");
        return "Failed to initialize Logitech driver. Please install LogitechDriver.exe";
    }
    "Bot started successfully"
}

fn generate_random_title() -> String {
    let rng = rand::thread_rng();
    let random_string: String = rng
        .sample_iter(&Alphanumeric)
        .take(10)
        .map(char::from)
        .collect();

    random_string
}

/// Update the global rotations list with custom rotations
pub fn update_rotations_with_custom(custom_rotations: Vec<RotationConfig>) {
    if let Ok(mut rotations) = ROTATIONS.lock() {
        // Remove existing custom rotations
        rotations.retain(|r| !r.is_custom.unwrap_or(false));

        // Add updated custom rotations
        rotations.extend(custom_rotations);

        log(&format!(
            "Updated global rotations list with {} custom rotations",
            rotations
                .iter()
                .filter(|r| r.is_custom.unwrap_or(false))
                .count()
        ));

        // Update keybinds for all rotations (including new custom ones)
        if let Err(e) = merge_keybinds(rotations.clone()) {
            log_error(&format!(
                "Failed to merge keybinds after updating custom rotations: {}",
                e
            ));
        } else {
            log("Successfully updated keybinds for custom rotations");
        }
    } else {
        log_error("Failed to lock ROTATIONS mutex for updating custom rotations");
    }
}

/// Get the current rotations list (including custom rotations)
#[tauri::command]
fn get_current_rotations() -> Vec<RotationConfig> {
    ROTATIONS.lock().unwrap().clone()
}

/// Clean up old rotation files from the addon folder
fn cleanup_old_rotation_files() {
    use crate::constants::ADDON_FOLDER_TO_COPY_PATH;

    let addon_path = Path::new(ADDON_FOLDER_TO_COPY_PATH);
    if !addon_path.exists() {
        return;
    }

    // Walk through all directories in the addon folder
    if let Ok(entries) = fs::read_dir(addon_path) {
        for entry in entries.flatten() {
            if entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false) {
                let dir_path = entry.path();
                let dir_name = dir_path.file_name().unwrap().to_string_lossy();

                // Skip Libs and core files
                if dir_name == "Libs" || dir_name.ends_with(".lua") || dir_name.ends_with(".toc") {
                    continue;
                }

                // Clean up old files in rotation directories
                let rotation_lua_path = dir_path.join("rotation.lua");
                let rotation_objects_path = dir_path.join("rotation_objects.lua");
                let priorities_lua_path = dir_path.join("Priorities.lua");

                // Remove old rotation.lua if Priorities.lua doesn't exist
                if rotation_lua_path.exists() && !priorities_lua_path.exists() {
                    if let Err(e) = fs::rename(&rotation_lua_path, &priorities_lua_path) {
                        log_error(&format!(
                            "Failed to rename rotation.lua to Priorities.lua in {}: {}",
                            dir_name, e
                        ));
                    } else {
                        log(&format!(
                            "Renamed rotation.lua to Priorities.lua in {}",
                            dir_name
                        ));
                    }
                } else if rotation_lua_path.exists() && priorities_lua_path.exists() {
                    // If both exist, remove the old rotation.lua
                    if let Err(e) = fs::remove_file(&rotation_lua_path) {
                        log_error(&format!(
                            "Failed to remove old rotation.lua in {}: {}",
                            dir_name, e
                        ));
                    } else {
                        log(&format!("Removed old rotation.lua in {}", dir_name));
                    }
                }

                // Always remove rotation_objects.lua since we use shared RStatus.lua
                if rotation_objects_path.exists() {
                    if let Err(e) = fs::remove_file(&rotation_objects_path) {
                        log_error(&format!(
                            "Failed to remove rotation_objects.lua in {}: {}",
                            dir_name, e
                        ));
                    } else {
                        log(&format!("Removed old rotation_objects.lua in {}", dir_name));
                    }
                }
            }
        }
    }
}

mod custom_rotations;

fn main() {
    Logger::try_with_str("info")
        .unwrap()
        .log_to_file(FileSpec::default().directory("logs")) // Log to a file
        .rotate(
            // If the program runs long enough,
            Criterion::Age(Age::Day), // - create a new file every day
            Naming::Timestamps,       // - let the rotated files have a timestamp in their name
            Cleanup::KeepLogFiles(7), // - keep at most 7 log files
        )
        .duplicate_to_stderr(Duplicate::Info) // Also print Info+ logs to the console
        .write_mode(WriteMode::BufferAndFlush) // Use buffering for better performance
        .start()
        .unwrap_or_else(|e| panic!("Failed to start the logger: {}", e));

    log("Starting OptiStrike v1.0");

    tauri::Builder::default()
        .setup(|app| {
            let random_title = generate_random_title();
            log("Getting things ready...");
            let main_window = app.get_window("main").unwrap();
            main_window.set_title(&random_title).unwrap();
            main_window.show().unwrap();
            main_window.unminimize().unwrap();
            main_window.set_focus().unwrap();

            main_window.listen("stop", |_event| {
                log("OptiStrike stopped");
            });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            start_bot,
            save_settings,
            get_settings,
            save_activation_key,
            get_rotations,
            get_current_rotations,
            save_app_settings,
            initialize,
            install_driver,
            get_keybinds,
            set_keybinds,
            get_activation_key,
            custom_rotations::save_custom_rotation,
            custom_rotations::load_custom_rotations,
            custom_rotations::delete_custom_rotation,
            generate_custom_rotation_lua
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
