local addonName, OPTI = ...
local main_frame = <PERSON><PERSON><PERSON>rame("Frame", "OptiFrame", UIParent)
main_frame:RegisterEvent("PLAYER_TARGET_CHANGED")
main_frame:RegisterEvent("UNIT_HEALTH")
main_frame:RegisterEvent("COMBAT_LOG_EVENT_UNFILTERED")

Player = {}
Target = {}
Spell = {}

-- CUSTOM_LOGIC --

local function OnUpdate(self, event) end

main_frame:SetScript("OnEvent", function(self, event)
	self[event](self)
end)
main_frame:SetScript("OnUpdate", OnUpdate)
